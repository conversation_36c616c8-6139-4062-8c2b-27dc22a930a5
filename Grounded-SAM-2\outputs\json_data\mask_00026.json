{"mask_name": "mask_00026.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"6": {"instance_id": 6, "class_name": "car", "x1": 1027, "y1": 133, "x2": 1088, "y2": 188, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1058, "y1": 368, "x2": 1187, "y2": 516, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1261, "y1": 355, "x2": 1413, "y2": 492, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 994, "y1": 70, "x2": 1031, "y2": 102, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1074, "y1": 68, "x2": 1110, "y2": 98, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1508, "y1": 22, "x2": 1546, "y2": 42, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 928, "y1": 9, "x2": 946, "y2": 25, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 983, "y1": 7, "x2": 1000, "y2": 21, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1256, "y1": 0, "x2": 1285, "y2": 15, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1461, "y1": 16, "x2": 1493, "y2": 33, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1430, "y1": 19, "x2": 1462, "y2": 39, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 903, "y1": 3, "x2": 922, "y2": 12, "logit": 0.0}, "18": {"instance_id": 18, "class_name": "car", "x1": 969, "y1": 0, "x2": 984, "y2": 13, "logit": 0.0}, "19": {"instance_id": 19, "class_name": "car", "x1": 947, "y1": 0, "x2": 956, "y2": 12, "logit": 0.0}}}