{"mask_name": "mask_00024.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"6": {"instance_id": 6, "class_name": "car", "x1": 1025, "y1": 123, "x2": 1082, "y2": 172, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1053, "y1": 310, "x2": 1162, "y2": 429, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1229, "y1": 298, "x2": 1356, "y2": 410, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 993, "y1": 65, "x2": 1028, "y2": 95, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1069, "y1": 63, "x2": 1104, "y2": 92, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1531, "y1": 25, "x2": 1567, "y2": 45, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 928, "y1": 9, "x2": 946, "y2": 23, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 982, "y1": 7, "x2": 997, "y2": 19, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1266, "y1": 0, "x2": 1296, "y2": 17, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1482, "y1": 18, "x2": 1511, "y2": 37, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1451, "y1": 22, "x2": 1487, "y2": 43, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 904, "y1": 3, "x2": 920, "y2": 12, "logit": 0.0}, "18": {"instance_id": 18, "class_name": "car", "x1": 968, "y1": 0, "x2": 982, "y2": 12, "logit": 0.0}, "19": {"instance_id": 19, "class_name": "car", "x1": 946, "y1": 0, "x2": 955, "y2": 11, "logit": 0.0}}}