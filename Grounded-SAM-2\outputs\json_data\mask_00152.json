{"mask_name": "mask_00152.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"30": {"instance_id": 30, "class_name": "car", "x1": 582, "y1": 318, "x2": 725, "y2": 458, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1057, "y1": 482, "x2": 1259, "y2": 729, "logit": 0.0}, "45": {"instance_id": 45, "class_name": "car", "x1": 1599, "y1": 15, "x2": 1645, "y2": 38, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 847, "y1": 62, "x2": 882, "y2": 93, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1419, "y1": 107, "x2": 1506, "y2": 165, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 974, "y1": 30, "x2": 1007, "y2": 54, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1484, "y1": 83, "x2": 1567, "y2": 143, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 985, "y1": 48, "x2": 1017, "y2": 81, "logit": 0.0}, "46": {"instance_id": 46, "class_name": "car", "x1": 870, "y1": 5, "x2": 899, "y2": 40, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1346, "y1": 0, "x2": 1376, "y2": 11, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1467, "y1": 0, "x2": 1503, "y2": 19, "logit": 0.0}, "47": {"instance_id": 47, "class_name": "car", "x1": 1121, "y1": 56, "x2": 1159, "y2": 77, "logit": 0.0}}}