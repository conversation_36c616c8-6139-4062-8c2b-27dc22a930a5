{"mask_name": "mask_00129.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"31": {"instance_id": 31, "class_name": "car", "x1": 813, "y1": 220, "x2": 903, "y2": 310, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1907, "y1": 338, "x2": 1919, "y2": 403, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 827, "y1": 584, "x2": 1070, "y2": 928, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1003, "y1": 120, "x2": 1064, "y2": 176, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 808, "y1": 96, "x2": 860, "y2": 141, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1712, "y1": 28, "x2": 1769, "y2": 56, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1367, "y1": 0, "x2": 1407, "y2": 14, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1513, "y1": 10, "x2": 1556, "y2": 39, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 868, "y1": 20, "x2": 894, "y2": 46, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1217, "y1": 16, "x2": 1259, "y2": 45, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1218, "y1": 36, "x2": 1263, "y2": 66, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 957, "y1": 15, "x2": 980, "y2": 35, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 945, "y1": 4, "x2": 966, "y2": 21, "logit": 0.0}}}