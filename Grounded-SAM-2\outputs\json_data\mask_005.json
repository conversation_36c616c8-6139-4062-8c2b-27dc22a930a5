{"mask_name": "mask_005.npy", "mask_height": 240, "mask_width": 360, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "bike", "x1": 82, "y1": 129, "x2": 109, "y2": 146, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "bike", "x1": 82, "y1": 129, "x2": 109, "y2": 146, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "bike", "x1": 96, "y1": 134, "x2": 109, "y2": 146, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "bike", "x1": 82, "y1": 134, "x2": 109, "y2": 146, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "bike", "x1": 196, "y1": 236, "x2": 198, "y2": 239, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "bike", "x1": 186, "y1": 238, "x2": 195, "y2": 239, "logit": 0.0}}}