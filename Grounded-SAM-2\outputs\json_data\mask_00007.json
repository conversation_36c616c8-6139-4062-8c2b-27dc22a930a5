{"mask_name": "mask_00007.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "car", "x1": 1123, "y1": 22, "x2": 1148, "y2": 42, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1107, "y1": 131, "x2": 1167, "y2": 180, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1022, "y1": 137, "x2": 1075, "y2": 190, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1695, "y1": 53, "x2": 1745, "y2": 77, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 973, "y1": 43, "x2": 1001, "y2": 65, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1001, "y1": 73, "x2": 1039, "y2": 105, "logit": 0.0}, "7": {"instance_id": 7, "class_name": "car", "x1": 1251, "y1": 7, "x2": 1276, "y2": 21, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1622, "y1": 50, "x2": 1667, "y2": 77, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1337, "y1": 11, "x2": 1372, "y2": 32, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1030, "y1": 41, "x2": 1055, "y2": 62, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 916, "y1": 8, "x2": 932, "y2": 19, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1618, "y1": 45, "x2": 1639, "y2": 64, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 956, "y1": 6, "x2": 971, "y2": 16, "logit": 0.0}, "14": {"instance_id": 14, "class_name": "car", "x1": 1618, "y1": 44, "x2": 1645, "y2": 65, "logit": 0.0}, "15": {"instance_id": 15, "class_name": "car", "x1": 1170, "y1": 0, "x2": 1175, "y2": 9, "logit": 0.0}, "16": {"instance_id": 16, "class_name": "car", "x1": 992, "y1": 0, "x2": 1009, "y2": 6, "logit": 0.0}}}