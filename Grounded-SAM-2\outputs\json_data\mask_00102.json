{"mask_name": "mask_00102.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"13": {"instance_id": 13, "class_name": "car", "x1": 1021, "y1": 159, "x2": 1085, "y2": 221, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 906, "y1": 118, "x2": 959, "y2": 174, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1298, "y1": 65, "x2": 1355, "y2": 104, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1098, "y1": 109, "x2": 1152, "y2": 161, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1611, "y1": 18, "x2": 1663, "y2": 42, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 916, "y1": 66, "x2": 953, "y2": 104, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 862, "y1": 32, "x2": 892, "y2": 56, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 969, "y1": 41, "x2": 1004, "y2": 69, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1346, "y1": 16, "x2": 1384, "y2": 36, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1114, "y1": 4, "x2": 1142, "y2": 25, "logit": 0.0}, "35": {"instance_id": 35, "class_name": "car", "x1": 882, "y1": 0, "x2": 901, "y2": 17, "logit": 0.0}, "36": {"instance_id": 36, "class_name": "car", "x1": 1668, "y1": 26, "x2": 1717, "y2": 54, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1878, "y1": 49, "x2": 1919, "y2": 86, "logit": 0.0}, "38": {"instance_id": 38, "class_name": "car", "x1": 1711, "y1": 7, "x2": 1724, "y2": 24, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1088, "y1": 0, "x2": 1114, "y2": 12, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1313, "y1": 0, "x2": 1340, "y2": 8, "logit": 0.0}, "40": {"instance_id": 40, "class_name": "car", "x1": 933, "y1": 0, "x2": 950, "y2": 10, "logit": 0.0}}}