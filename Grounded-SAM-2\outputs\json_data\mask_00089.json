{"mask_name": "mask_00089.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"22": {"instance_id": 22, "class_name": "car", "x1": 1056, "y1": 61, "x2": 1093, "y2": 93, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1012, "y1": 86, "x2": 1054, "y2": 127, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1485, "y1": 38, "x2": 1535, "y2": 67, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1371, "y1": 0, "x2": 1424, "y2": 20, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1212, "y1": 36, "x2": 1253, "y2": 64, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 910, "y1": 71, "x2": 950, "y2": 113, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 874, "y1": 9, "x2": 899, "y2": 38, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 933, "y1": 41, "x2": 963, "y2": 69, "logit": 0.0}, "28": {"instance_id": 28, "class_name": "car", "x1": 874, "y1": 15, "x2": 898, "y2": 38, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 954, "y1": 26, "x2": 982, "y2": 48, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1080, "y1": 0, "x2": 1107, "y2": 15, "logit": 0.0}, "33": {"instance_id": 33, "class_name": "car", "x1": 874, "y1": 16, "x2": 898, "y2": 38, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 933, "y1": 42, "x2": 963, "y2": 69, "logit": 0.0}}}