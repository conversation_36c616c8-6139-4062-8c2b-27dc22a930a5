{"mask_name": "mask_00115.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"13": {"instance_id": 13, "class_name": "car", "x1": 1042, "y1": 354, "x2": 1165, "y2": 495, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 897, "y1": 209, "x2": 982, "y2": 306, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1446, "y1": 121, "x2": 1533, "y2": 182, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1190, "y1": 239, "x2": 1293, "y2": 337, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1481, "y1": 5, "x2": 1530, "y2": 28, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 893, "y1": 111, "x2": 941, "y2": 161, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 841, "y1": 54, "x2": 880, "y2": 85, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 982, "y1": 65, "x2": 1024, "y2": 104, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1251, "y1": 0, "x2": 1284, "y2": 19, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1171, "y1": 17, "x2": 1190, "y2": 41, "logit": 0.0}, "35": {"instance_id": 35, "class_name": "car", "x1": 876, "y1": 0, "x2": 897, "y2": 28, "logit": 0.0}, "36": {"instance_id": 36, "class_name": "car", "x1": 1499, "y1": 11, "x2": 1536, "y2": 33, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1673, "y1": 27, "x2": 1728, "y2": 59, "logit": 0.0}, "38": {"instance_id": 38, "class_name": "car", "x1": 1709, "y1": 8, "x2": 1720, "y2": 24, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1137, "y1": 0, "x2": 1162, "y2": 14, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "40": {"instance_id": 40, "class_name": "car", "x1": 938, "y1": 0, "x2": 962, "y2": 19, "logit": 0.0}}}