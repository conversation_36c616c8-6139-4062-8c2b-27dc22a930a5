{"mask_name": "mask_00036.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"6": {"instance_id": 6, "class_name": "car", "x1": 1040, "y1": 205, "x2": 1127, "y2": 284, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1620, "y1": 1016, "x2": 1869, "y2": 1079, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1002, "y1": 94, "x2": 1046, "y2": 135, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1099, "y1": 91, "x2": 1147, "y2": 132, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1436, "y1": 12, "x2": 1468, "y2": 28, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 931, "y1": 12, "x2": 950, "y2": 30, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 992, "y1": 8, "x2": 1011, "y2": 26, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1217, "y1": 0, "x2": 1239, "y2": 6, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1393, "y1": 7, "x2": 1421, "y2": 23, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1356, "y1": 9, "x2": 1384, "y2": 28, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 910, "y1": 0, "x2": 930, "y2": 14, "logit": 0.0}, "18": {"instance_id": 18, "class_name": "car", "x1": 979, "y1": 0, "x2": 995, "y2": 16, "logit": 0.0}, "19": {"instance_id": 19, "class_name": "car", "x1": 950, "y1": 0, "x2": 964, "y2": 14, "logit": 0.0}}}