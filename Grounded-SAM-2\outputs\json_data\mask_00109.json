{"mask_name": "mask_00109.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"13": {"instance_id": 13, "class_name": "car", "x1": 1030, "y1": 229, "x2": 1115, "y2": 320, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 903, "y1": 156, "x2": 971, "y2": 227, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1362, "y1": 88, "x2": 1430, "y2": 137, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1135, "y1": 160, "x2": 1207, "y2": 227, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1538, "y1": 10, "x2": 1587, "y2": 38, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 905, "y1": 86, "x2": 948, "y2": 128, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 853, "y1": 44, "x2": 885, "y2": 70, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 976, "y1": 54, "x2": 1015, "y2": 86, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1292, "y1": 7, "x2": 1326, "y2": 26, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1132, "y1": 9, "x2": 1154, "y2": 32, "logit": 0.0}, "35": {"instance_id": 35, "class_name": "car", "x1": 877, "y1": 0, "x2": 899, "y2": 23, "logit": 0.0}, "36": {"instance_id": 36, "class_name": "car", "x1": 1576, "y1": 17, "x2": 1615, "y2": 42, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1760, "y1": 37, "x2": 1823, "y2": 71, "logit": 0.0}, "38": {"instance_id": 38, "class_name": "car", "x1": 1710, "y1": 7, "x2": 1722, "y2": 23, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1113, "y1": 0, "x2": 1140, "y2": 18, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "40": {"instance_id": 40, "class_name": "car", "x1": 935, "y1": 0, "x2": 956, "y2": 14, "logit": 0.0}}}