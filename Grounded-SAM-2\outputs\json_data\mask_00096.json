{"mask_name": "mask_00096.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"22": {"instance_id": 22, "class_name": "car", "x1": 1074, "y1": 78, "x2": 1119, "y2": 120, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1016, "y1": 114, "x2": 1066, "y2": 163, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1409, "y1": 25, "x2": 1453, "y2": 49, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1317, "y1": 0, "x2": 1380, "y2": 14, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1250, "y1": 49, "x2": 1297, "y2": 81, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 907, "y1": 90, "x2": 952, "y2": 136, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 866, "y1": 20, "x2": 895, "y2": 47, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 923, "y1": 51, "x2": 957, "y2": 84, "logit": 0.0}, "28": {"instance_id": 28, "class_name": "car", "x1": 866, "y1": 23, "x2": 895, "y2": 46, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 960, "y1": 32, "x2": 992, "y2": 57, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1097, "y1": 0, "x2": 1124, "y2": 19, "logit": 0.0}, "33": {"instance_id": 33, "class_name": "car", "x1": 866, "y1": 24, "x2": 895, "y2": 46, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 924, "y1": 51, "x2": 958, "y2": 84, "logit": 0.0}}}