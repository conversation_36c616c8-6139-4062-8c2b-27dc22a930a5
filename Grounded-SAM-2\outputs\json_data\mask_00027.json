{"mask_name": "mask_00027.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"6": {"instance_id": 6, "class_name": "car", "x1": 1028, "y1": 138, "x2": 1091, "y2": 192, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1060, "y1": 385, "x2": 1196, "y2": 540, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1273, "y1": 371, "x2": 1433, "y2": 520, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 995, "y1": 72, "x2": 1032, "y2": 104, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1076, "y1": 69, "x2": 1112, "y2": 100, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1504, "y1": 21, "x2": 1540, "y2": 40, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 929, "y1": 10, "x2": 947, "y2": 25, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 985, "y1": 9, "x2": 1001, "y2": 21, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1254, "y1": 0, "x2": 1282, "y2": 15, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1457, "y1": 16, "x2": 1488, "y2": 33, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1425, "y1": 18, "x2": 1458, "y2": 38, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 903, "y1": 3, "x2": 923, "y2": 12, "logit": 0.0}, "18": {"instance_id": 18, "class_name": "car", "x1": 970, "y1": 0, "x2": 985, "y2": 14, "logit": 0.0}, "19": {"instance_id": 19, "class_name": "car", "x1": 947, "y1": 0, "x2": 957, "y2": 13, "logit": 0.0}}}