{"mask_name": "mask_00140.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"31": {"instance_id": 31, "class_name": "car", "x1": 584, "y1": 478, "x2": 780, "y2": 696, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1025, "y1": 203, "x2": 1117, "y2": 292, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 750, "y1": 159, "x2": 827, "y2": 223, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1585, "y1": 14, "x2": 1623, "y2": 35, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1425, "y1": 0, "x2": 1463, "y2": 21, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 862, "y1": 36, "x2": 891, "y2": 64, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1312, "y1": 39, "x2": 1367, "y2": 78, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1292, "y1": 59, "x2": 1350, "y2": 99, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 970, "y1": 28, "x2": 996, "y2": 52, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 959, "y1": 15, "x2": 985, "y2": 35, "logit": 0.0}}}