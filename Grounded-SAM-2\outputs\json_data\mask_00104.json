{"mask_name": "mask_00104.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"13": {"instance_id": 13, "class_name": "car", "x1": 1023, "y1": 175, "x2": 1090, "y2": 240, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 906, "y1": 126, "x2": 962, "y2": 187, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1314, "y1": 70, "x2": 1373, "y2": 112, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1106, "y1": 121, "x2": 1165, "y2": 176, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1590, "y1": 16, "x2": 1642, "y2": 39, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 913, "y1": 72, "x2": 951, "y2": 111, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 860, "y1": 33, "x2": 890, "y2": 60, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 971, "y1": 45, "x2": 1007, "y2": 74, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1330, "y1": 13, "x2": 1367, "y2": 33, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1119, "y1": 5, "x2": 1147, "y2": 27, "logit": 0.0}, "35": {"instance_id": 35, "class_name": "car", "x1": 880, "y1": 0, "x2": 900, "y2": 18, "logit": 0.0}, "36": {"instance_id": 36, "class_name": "car", "x1": 1639, "y1": 25, "x2": 1687, "y2": 50, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1842, "y1": 46, "x2": 1919, "y2": 83, "logit": 0.0}, "38": {"instance_id": 38, "class_name": "car", "x1": 1711, "y1": 7, "x2": 1724, "y2": 22, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1095, "y1": 0, "x2": 1122, "y2": 13, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1308, "y1": 0, "x2": 1329, "y2": 6, "logit": 0.0}, "40": {"instance_id": 40, "class_name": "car", "x1": 932, "y1": 0, "x2": 951, "y2": 11, "logit": 0.0}}}