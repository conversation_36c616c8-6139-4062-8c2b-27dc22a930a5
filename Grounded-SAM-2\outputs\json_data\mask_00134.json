{"mask_name": "mask_00134.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"31": {"instance_id": 31, "class_name": "car", "x1": 749, "y1": 297, "x2": 868, "y2": 421, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1013, "y1": 150, "x2": 1084, "y2": 215, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 788, "y1": 120, "x2": 848, "y2": 173, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1650, "y1": 24, "x2": 1697, "y2": 45, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1328, "y1": 0, "x2": 1370, "y2": 10, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1472, "y1": 5, "x2": 1510, "y2": 31, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 864, "y1": 30, "x2": 892, "y2": 53, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1256, "y1": 24, "x2": 1300, "y2": 54, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1247, "y1": 46, "x2": 1297, "y2": 79, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 962, "y1": 20, "x2": 986, "y2": 43, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 953, "y1": 8, "x2": 974, "y2": 27, "logit": 0.0}}}