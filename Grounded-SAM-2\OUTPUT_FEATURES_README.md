# Output Folder Features for Grounded SAM2 Tracking Demo

This document describes the new output folder functionality added to `grounded_sam2_tracking_demo_custom_video_input_gd1.0_hf_model.py`.

## New Features

### 1. Output Folder Configuration
- **Parameter**: `OUTPUT_FOLDER`
- **Default**: `None` (disabled)
- **Usage**: Set to a folder path (e.g., `"./output"`) to enable output features

### 2. Video Output
- Saves processed videos with segmentation overlays to the specified output folder
- Creates a `processed_videos/` subfolder
- Video filename: `processed_[original_video_name].mp4`

### 3. Contour Data Export
- **Parameter**: `SAVE_CONTOURS`
- **Default**: `True`
- **Functionality**: Extracts object contours from segmentation masks and saves as JSON files

## Usage

### Enable All Features
```python
OUTPUT_FOLDER = "./output"  # Enable output folder
SAVE_CONTOURS = True        # Enable contour extraction
```

### Video Output Only
```python
OUTPUT_FOLDER = "./output"  # Enable output folder
SAVE_CONTOURS = False       # Disable contour extraction
```

### Disable All Features (Original Behavior)
```python
OUTPUT_FOLDER = None        # Disable output folder
SAVE_CONTOURS = True        # Ignored when OUTPUT_FOLDER is None
```

## Output Structure

When `OUTPUT_FOLDER` is specified, the following structure is created:

```
output/
├── processed_videos/
│   └── processed_yellow_ball_demo.mp4
└── contours/
    ├── contours_data.json           # All frames in one file
    ├── frame_00000_contours.json    # Individual frame files
    ├── frame_00001_contours.json
    └── ...
```

## Contour Data Format

### Consolidated File (`contours_data.json`)
```json
{
  "frame_00000": {
    "object_1": {
      "label": "yellow ball",
      "contours": [
        [[x1, y1], [x2, y2], [x3, y3], ...],
        [[x4, y4], [x5, y5], [x6, y6], ...]
      ]
    }
  },
  "frame_00001": {
    ...
  }
}
```

### Individual Frame Files (`frame_XXXXX_contours.json`)
```json
{
  "object_1": {
    "label": "yellow ball",
    "contours": [
      [[x1, y1], [x2, y2], [x3, y3], ...],
      [[x4, y4], [x5, y5], [x6, y6], ...]
    ]
  }
}
```

## Implementation Details

### New Functions Added
1. `extract_contours_from_mask(mask)` - Extracts contours from binary masks
2. `save_contours_to_file(contours_data, output_path)` - Saves contour data as JSON
3. `setup_output_folder(output_folder)` - Creates output folder structure

### Minimal Changes
- Added import for `json` module
- Added configuration parameters at the top
- Added helper functions before model initialization
- Modified visualization loop to extract contours
- Enhanced final video creation section

### Backward Compatibility
- All existing functionality preserved
- Script works exactly as before when `OUTPUT_FOLDER = None`
- No changes to core detection/tracking logic
- Original output files still created in their original locations

## Notes

- Contours are simplified using `cv2.approxPolyDP` to reduce file size while preserving shape
- Both consolidated and individual frame contour files are saved for flexibility
- Output folders are created automatically if they don't exist
- The script provides progress feedback when saving outputs
