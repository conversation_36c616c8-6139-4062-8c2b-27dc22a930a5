{"mask_name": "mask_00059.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"10": {"instance_id": 10, "class_name": "car", "x1": 1247, "y1": 289, "x2": 1370, "y2": 406, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1015, "y1": 279, "x2": 1122, "y2": 391, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "20": {"instance_id": 20, "class_name": "car", "x1": 1424, "y1": 112, "x2": 1501, "y2": 168, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 927, "y1": 25, "x2": 955, "y2": 51, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1217, "y1": 0, "x2": 1238, "y2": 4, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 955, "y1": 13, "x2": 976, "y2": 30, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1005, "y1": 22, "x2": 1031, "y2": 49, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1009, "y1": 16, "x2": 1031, "y2": 31, "logit": 0.0}, "23": {"instance_id": 23, "class_name": "car", "x1": 1144, "y1": 64, "x2": 1178, "y2": 93, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 927, "y1": 9, "x2": 952, "y2": 25, "logit": 0.0}, "24": {"instance_id": 24, "class_name": "car", "x1": 898, "y1": 0, "x2": 912, "y2": 14, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1109, "y1": 6, "x2": 1136, "y2": 25, "logit": 0.0}}}