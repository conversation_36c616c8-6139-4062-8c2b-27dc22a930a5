{"mask_name": "mask_00141.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"30": {"instance_id": 30, "class_name": "car", "x1": 741, "y1": 168, "x2": 820, "y2": 237, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1029, "y1": 218, "x2": 1124, "y2": 312, "logit": 0.0}, "45": {"instance_id": 45, "class_name": "car", "x1": 1736, "y1": 30, "x2": 1793, "y2": 58, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 527, "y1": 539, "x2": 753, "y2": 797, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 862, "y1": 42, "x2": 891, "y2": 66, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1301, "y1": 64, "x2": 1362, "y2": 104, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 962, "y1": 16, "x2": 988, "y2": 37, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1327, "y1": 42, "x2": 1380, "y2": 83, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 971, "y1": 30, "x2": 998, "y2": 55, "logit": 0.0}, "46": {"instance_id": 46, "class_name": "car", "x1": 876, "y1": 0, "x2": 900, "y2": 28, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1418, "y1": 0, "x2": 1453, "y2": 20, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1569, "y1": 13, "x2": 1610, "y2": 31, "logit": 0.0}, "47": {"instance_id": 47, "class_name": "car", "x1": 1122, "y1": 56, "x2": 1158, "y2": 77, "logit": 0.0}}}