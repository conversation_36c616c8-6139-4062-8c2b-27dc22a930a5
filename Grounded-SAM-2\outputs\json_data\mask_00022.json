{"mask_name": "mask_00022.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"6": {"instance_id": 6, "class_name": "car", "x1": 1023, "y1": 114, "x2": 1077, "y2": 161, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1049, "y1": 278, "x2": 1147, "y2": 382, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1209, "y1": 266, "x2": 1321, "y2": 365, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 992, "y1": 63, "x2": 1026, "y2": 92, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1065, "y1": 60, "x2": 1098, "y2": 88, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1546, "y1": 29, "x2": 1584, "y2": 48, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 928, "y1": 9, "x2": 945, "y2": 23, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 980, "y1": 6, "x2": 995, "y2": 19, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1274, "y1": 0, "x2": 1305, "y2": 19, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1502, "y1": 21, "x2": 1526, "y2": 39, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1467, "y1": 25, "x2": 1503, "y2": 48, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 902, "y1": 4, "x2": 919, "y2": 13, "logit": 0.0}, "18": {"instance_id": 18, "class_name": "car", "x1": 964, "y1": 0, "x2": 979, "y2": 12, "logit": 0.0}, "19": {"instance_id": 19, "class_name": "car", "x1": 945, "y1": 0, "x2": 955, "y2": 12, "logit": 0.0}}}