{"mask_name": "mask_00009.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "car", "x1": 1130, "y1": 23, "x2": 1156, "y2": 43, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1113, "y1": 140, "x2": 1180, "y2": 194, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1025, "y1": 146, "x2": 1082, "y2": 203, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1675, "y1": 50, "x2": 1724, "y2": 74, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 976, "y1": 45, "x2": 1005, "y2": 69, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1003, "y1": 77, "x2": 1044, "y2": 110, "logit": 0.0}, "7": {"instance_id": 7, "class_name": "car", "x1": 1245, "y1": 6, "x2": 1269, "y2": 20, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1599, "y1": 48, "x2": 1645, "y2": 73, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1329, "y1": 9, "x2": 1363, "y2": 31, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1033, "y1": 43, "x2": 1061, "y2": 65, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 919, "y1": 7, "x2": 934, "y2": 20, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1597, "y1": 48, "x2": 1611, "y2": 66, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 960, "y1": 5, "x2": 975, "y2": 16, "logit": 0.0}, "14": {"instance_id": 14, "class_name": "car", "x1": 1598, "y1": 44, "x2": 1618, "y2": 66, "logit": 0.0}, "15": {"instance_id": 15, "class_name": "car", "x1": 1170, "y1": 0, "x2": 1177, "y2": 17, "logit": 0.0}, "16": {"instance_id": 16, "class_name": "car", "x1": 997, "y1": 0, "x2": 1015, "y2": 7, "logit": 0.0}}}