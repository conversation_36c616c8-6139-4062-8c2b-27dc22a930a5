{"mask_name": "mask_00148.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"30": {"instance_id": 30, "class_name": "car", "x1": 667, "y1": 241, "x2": 774, "y2": 340, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1043, "y1": 337, "x2": 1188, "y2": 498, "logit": 0.0}, "45": {"instance_id": 45, "class_name": "car", "x1": 1647, "y1": 20, "x2": 1698, "y2": 45, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 855, "y1": 48, "x2": 887, "y2": 81, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1367, "y1": 86, "x2": 1440, "y2": 138, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 969, "y1": 23, "x2": 999, "y2": 47, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1410, "y1": 64, "x2": 1480, "y2": 115, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 979, "y1": 39, "x2": 1010, "y2": 70, "logit": 0.0}, "46": {"instance_id": 46, "class_name": "car", "x1": 872, "y1": 2, "x2": 899, "y2": 35, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1372, "y1": 0, "x2": 1404, "y2": 14, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1503, "y1": 6, "x2": 1542, "y2": 27, "logit": 0.0}, "47": {"instance_id": 47, "class_name": "car", "x1": 1120, "y1": 55, "x2": 1160, "y2": 77, "logit": 0.0}}}