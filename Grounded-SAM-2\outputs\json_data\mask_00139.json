{"mask_name": "mask_00139.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"31": {"instance_id": 31, "class_name": "car", "x1": 628, "y1": 430, "x2": 806, "y2": 627, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1022, "y1": 191, "x2": 1108, "y2": 273, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 760, "y1": 148, "x2": 831, "y2": 212, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1592, "y1": 16, "x2": 1639, "y2": 38, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1434, "y1": 0, "x2": 1470, "y2": 24, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 864, "y1": 30, "x2": 893, "y2": 61, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1299, "y1": 36, "x2": 1351, "y2": 74, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1282, "y1": 58, "x2": 1340, "y2": 95, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 968, "y1": 27, "x2": 994, "y2": 51, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 957, "y1": 14, "x2": 982, "y2": 33, "logit": 0.0}}}