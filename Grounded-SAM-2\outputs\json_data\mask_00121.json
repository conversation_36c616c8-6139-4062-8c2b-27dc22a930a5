{"mask_name": "mask_00121.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"31": {"instance_id": 31, "class_name": "car", "x1": 870, "y1": 144, "x2": 932, "y2": 205, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1571, "y1": 170, "x2": 1690, "y2": 246, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1299, "y1": 409, "x2": 1475, "y2": 594, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1069, "y1": 649, "x2": 1285, "y2": 962, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 884, "y1": 295, "x2": 1010, "y2": 433, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 990, "y1": 84, "x2": 1040, "y2": 125, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 834, "y1": 67, "x2": 875, "y2": 104, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1850, "y1": 46, "x2": 1919, "y2": 75, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1433, "y1": 0, "x2": 1472, "y2": 24, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1599, "y1": 20, "x2": 1648, "y2": 48, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 876, "y1": 13, "x2": 895, "y2": 34, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1174, "y1": 8, "x2": 1200, "y2": 29, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1178, "y1": 25, "x2": 1215, "y2": 49, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 949, "y1": 8, "x2": 968, "y2": 25, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 937, "y1": 0, "x2": 955, "y2": 13, "logit": 0.0}}}