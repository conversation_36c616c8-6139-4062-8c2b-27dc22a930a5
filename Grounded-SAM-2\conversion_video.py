#!/usr/bin/env python3
"""
AVI to MP4 Video Conversion Script

This script converts all AVI video files to MP4 format using H.264 codec
for videos located in the assets directory.

Usage:
    python conversion_video.py [--keep-originals] [--quality QUALITY] [--input-dir INPUT_DIR] [--codec CODEC]

Arguments:
    --keep-originals    Keep original AVI files after conversion (default: False)
    --quality          Video quality/CRF value (0-51, lower is better, default: 23)
    --input-dir        Input directory path (default: assets)
    --codec            Video codec (default: libx264)
"""

import os
import argparse
import subprocess
import sys
from pathlib import Path
import cv2

def check_ffmpeg():
    """Check if FFmpeg is available in the system."""
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def get_video_info(video_path):
    """Get video information using OpenCV."""
    try:
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            return None
        
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        duration = frame_count / fps if fps > 0 else 0
        
        cap.release()
        
        return {
            'fps': fps,
            'frame_count': frame_count,
            'width': width,
            'height': height,
            'duration': duration
        }
    except Exception as e:
        print(f"  ⚠ Warning: Could not read video info: {e}")
        return None

def convert_avi_to_mp4_ffmpeg(input_file, output_file, quality=23, codec='libx264'):
    """Convert AVI to MP4 using FFmpeg."""
    try:
        cmd = [
            'ffmpeg',
            '-i', str(input_file),
            '-c:v', codec,
            '-crf', str(quality),
            '-c:a', 'aac',
            '-movflags', '+faststart',
            '-y',  # Overwrite output file if it exists
            str(output_file)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            return True, None
        else:
            return False, result.stderr
            
    except Exception as e:
        return False, str(e)

def convert_avi_to_mp4_opencv(input_file, output_file):
    """Convert AVI to MP4 using OpenCV (fallback method)."""
    try:
        cap = cv2.VideoCapture(str(input_file))
        if not cap.isOpened():
            return False, "Could not open input video"
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        # Define codec and create VideoWriter
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(output_file), fourcc, fps, (width, height))
        
        frame_count = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            out.write(frame)
            frame_count += 1
        
        cap.release()
        out.release()
        
        return True, None
        
    except Exception as e:
        return False, str(e)

def convert_videos(input_dir, quality=23, codec='libx264', keep_originals=False, use_ffmpeg=True):
    """
    Convert all AVI files in the specified directory to MP4 format.
    
    Args:
        input_dir (str): Directory containing AVI files
        quality (int): Video quality/CRF value (0-51 for FFmpeg)
        codec (str): Video codec to use
        keep_originals (bool): Whether to keep original AVI files
        use_ffmpeg (bool): Whether to use FFmpeg (True) or OpenCV (False)
    
    Returns:
        tuple: (converted_count, error_count)
    """
    input_path = Path(input_dir)
    
    # Check if input directory exists
    if not input_path.exists():
        print(f"Error: Input directory '{input_dir}' does not exist.")
        return 0, 0
    
    if not input_path.is_dir():
        print(f"Error: '{input_dir}' is not a directory.")
        return 0, 0
    
    # Find all AVI files (case-insensitive)
    avi_extensions = ['.avi', '.AVI', '.Avi']
    avi_files = []
    
    for ext in avi_extensions:
        avi_files.extend(input_path.glob(f'*{ext}'))
    
    if not avi_files:
        print(f"No AVI files found in '{input_dir}'.")
        return 0, 0
    
    print(f"Found {len(avi_files)} AVI file(s) in '{input_dir}'")
    print(f"Conversion method: {'FFmpeg' if use_ffmpeg else 'OpenCV'}")
    if use_ffmpeg:
        print(f"Video codec: {codec}")
        print(f"Quality (CRF): {quality}")
    print(f"Keep originals: {keep_originals}")
    print("-" * 60)
    
    converted_count = 0
    error_count = 0
    
    for avi_file in avi_files:
        try:
            # Create output filename with .mp4 extension
            output_filename = avi_file.stem + '.mp4'
            output_path = input_path / output_filename
            
            print(f"Converting: {avi_file.name} -> {output_filename}")
            
            # Get video info
            video_info = get_video_info(avi_file)
            if video_info:
                print(f"  Input: {video_info['width']}x{video_info['height']}, "
                      f"{video_info['fps']:.2f} fps, {video_info['duration']:.1f}s")
            
            # Convert the video
            if use_ffmpeg:
                success, error_msg = convert_avi_to_mp4_ffmpeg(avi_file, output_path, quality, codec)
            else:
                success, error_msg = convert_avi_to_mp4_opencv(avi_file, output_path)
            
            if success:
                print(f"  ✓ Successfully converted to {output_filename}")
                
                # Verify output file
                if output_path.exists() and output_path.stat().st_size > 0:
                    converted_count += 1
                    
                    # Delete original file if requested
                    if not keep_originals:
                        try:
                            avi_file.unlink()
                            print(f"  ✓ Deleted original file: {avi_file.name}")
                        except Exception as e:
                            print(f"  ⚠ Warning: Could not delete original file {avi_file.name}: {e}")
                else:
                    print(f"  ✗ Output file is empty or missing")
                    error_count += 1
            else:
                print(f"  ✗ Conversion failed: {error_msg}")
                error_count += 1
            
        except Exception as e:
            print(f"  ✗ Error converting {avi_file.name}: {e}")
            error_count += 1
        
        print()  # Add blank line between files
    
    return converted_count, error_count

def main():
    """Main function to handle command line arguments and run conversion."""
    parser = argparse.ArgumentParser(
        description="Convert AVI files to MP4 format",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument(
        '--keep-originals',
        action='store_true',
        help='Keep original AVI files after conversion (default: False)'
    )
    
    parser.add_argument(
        '--quality',
        type=int,
        default=23,
        choices=range(0, 52),
        metavar='0-51',
        help='Video quality/CRF value (0-51, lower is better, default: 23)'
    )
    
    parser.add_argument(
        '--input-dir',
        type=str,
        default='assets',
        help='Input directory path (default: assets)'
    )
    
    parser.add_argument(
        '--codec',
        type=str,
        default='libx264',
        choices=['libx264', 'libx265', 'h264_nvenc'],
        help='Video codec (default: libx264)'
    )
    
    args = parser.parse_args()
    
    print("AVI to MP4 Video Conversion Script")
    print("=" * 60)
    
    # Check for FFmpeg availability
    use_ffmpeg = check_ffmpeg()
    if not use_ffmpeg:
        print("⚠ Warning: FFmpeg not found. Using OpenCV as fallback.")
        print("  Note: FFmpeg provides better quality and more codec options.")
        print("  Install FFmpeg for optimal results.")
        print()
    
    # Run the conversion
    converted, errors = convert_videos(
        input_dir=args.input_dir,
        quality=args.quality,
        codec=args.codec,
        keep_originals=args.keep_originals,
        use_ffmpeg=use_ffmpeg
    )
    
    # Print summary
    print("-" * 60)
    print("Conversion Summary:")
    print(f"  Successfully converted: {converted} files")
    print(f"  Errors encountered: {errors} files")
    print(f"  Total files processed: {converted + errors}")
    
    if converted > 0:
        print(f"\n✓ Conversion completed! {converted} AVI files converted to MP4.")
        if not args.keep_originals:
            print("  Original AVI files have been deleted.")
        else:
            print("  Original AVI files have been preserved.")
    
    if errors > 0:
        print(f"\n⚠ {errors} files could not be converted. Check the error messages above.")
        sys.exit(1)
    
    if converted == 0 and errors == 0:
        print("\nNo files to process.")

if __name__ == "__main__":
    main()
