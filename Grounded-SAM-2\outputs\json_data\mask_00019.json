{"mask_name": "mask_00019.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1181, "y1": 223, "x2": 1278, "y2": 303, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1044, "y1": 234, "x2": 1127, "y2": 321, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1572, "y1": 33, "x2": 1611, "y2": 54, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 987, "y1": 58, "x2": 1021, "y2": 85, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1018, "y1": 103, "x2": 1068, "y2": 144, "logit": 0.0}, "7": {"instance_id": 7, "class_name": "car", "x1": 1210, "y1": 0, "x2": 1231, "y2": 10, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1495, "y1": 28, "x2": 1535, "y2": 51, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1287, "y1": 0, "x2": 1317, "y2": 21, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1057, "y1": 55, "x2": 1089, "y2": 81, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 925, "y1": 6, "x2": 943, "y2": 22, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1531, "y1": 26, "x2": 1550, "y2": 42, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 970, "y1": 3, "x2": 990, "y2": 18, "logit": 0.0}, "14": {"instance_id": 14, "class_name": "car", "x1": 1510, "y1": 26, "x2": 1551, "y2": 48, "logit": 0.0}, "15": {"instance_id": 15, "class_name": "car", "x1": 1170, "y1": 0, "x2": 1182, "y2": 14, "logit": 0.0}, "16": {"instance_id": 16, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}}}