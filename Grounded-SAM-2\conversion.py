#!/usr/bin/env python3
"""
TIFF to JPEG Conversion Script

This script converts all TIFF image files (.tif and .tiff extensions) to JPEG format (.jpg)
for images located in the notebooks/videos/Test003 directory.

Usage:
    python conversion.py [--keep-originals] [--quality QUALITY] [--input-dir INPUT_DIR]

Arguments:
    --keep-originals    Keep original TIFF files after conversion (default: False)
    --quality          JPEG quality (1-100, default: 95)
    --input-dir        Input directory path (default: notebooks/videos/Test003)
"""

import os
import argparse
from pathlib import Path
from PIL import Image
import sys

def convert_tiff_to_jpeg(input_dir, quality=95, keep_originals=False):
    """
    Convert all TIFF files in the specified directory to JPEG format.
    
    Args:
        input_dir (str): Directory containing TIFF files
        quality (int): JPEG quality (1-100)
        keep_originals (bool): Whether to keep original TIFF files
    
    Returns:
        tuple: (converted_count, error_count)
    """
    input_path = Path(input_dir)
    
    # Check if input directory exists
    if not input_path.exists():
        print(f"Error: Input directory '{input_dir}' does not exist.")
        return 0, 0
    
    if not input_path.is_dir():
        print(f"Error: '{input_dir}' is not a directory.")
        return 0, 0
    
    # Find all TIFF files (case-insensitive)
    tiff_extensions = ['.tif', '.tiff', '.TIF', '.TIFF', '.Tif', '.Tiff']
    tiff_files = []
    
    for ext in tiff_extensions:
        tiff_files.extend(input_path.glob(f'*{ext}'))
    
    if not tiff_files:
        print(f"No TIFF files found in '{input_dir}'.")
        return 0, 0
    
    print(f"Found {len(tiff_files)} TIFF file(s) in '{input_dir}'")
    print(f"JPEG quality: {quality}")
    print(f"Keep originals: {keep_originals}")
    print("-" * 50)
    
    converted_count = 0
    error_count = 0
    
    for tiff_file in tiff_files:
        try:
            # Create output filename with .jpg extension
            output_filename = tiff_file.stem + '.jpg'
            output_path = input_path / output_filename
            
            print(f"Converting: {tiff_file.name} -> {output_filename}")
            
            # Open and convert the image
            with Image.open(tiff_file) as img:
                # Convert to RGB if necessary (TIFF might be in different modes)
                if img.mode in ('RGBA', 'LA', 'P'):
                    # Convert RGBA/LA/P to RGB
                    rgb_img = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'P':
                        img = img.convert('RGBA')
                    rgb_img.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
                    img = rgb_img
                elif img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Save as JPEG
                img.save(output_path, 'JPEG', quality=quality, optimize=True)
            
            print(f"  ✓ Successfully converted to {output_filename}")
            converted_count += 1
            
            # Delete original file if requested
            if not keep_originals:
                try:
                    tiff_file.unlink()
                    print(f"  ✓ Deleted original file: {tiff_file.name}")
                except Exception as e:
                    print(f"  ⚠ Warning: Could not delete original file {tiff_file.name}: {e}")
            
        except Exception as e:
            print(f"  ✗ Error converting {tiff_file.name}: {e}")
            error_count += 1
    
    return converted_count, error_count

def main():
    """Main function to handle command line arguments and run conversion."""
    parser = argparse.ArgumentParser(
        description="Convert TIFF files to JPEG format",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument(
        '--keep-originals',
        action='store_true',
        help='Keep original TIFF files after conversion (default: False)'
    )
    
    parser.add_argument(
        '--quality',
        type=int,
        default=95,
        choices=range(1, 101),
        metavar='1-100',
        help='JPEG quality (1-100, default: 95)'
    )
    
    parser.add_argument(
        '--input-dir',
        type=str,
        default='notebooks/videos/Test003',
        help='Input directory path (default: notebooks/videos/Test003)'
    )
    
    args = parser.parse_args()
    
    print("TIFF to JPEG Conversion Script")
    print("=" * 50)
    
    # Run the conversion
    converted, errors = convert_tiff_to_jpeg(
        input_dir=args.input_dir,
        quality=args.quality,
        keep_originals=args.keep_originals
    )
    
    # Print summary
    print("-" * 50)
    print("Conversion Summary:")
    print(f"  Successfully converted: {converted} files")
    print(f"  Errors encountered: {errors} files")
    print(f"  Total files processed: {converted + errors}")
    
    if converted > 0:
        print(f"\n✓ Conversion completed! {converted} TIFF files converted to JPEG.")
        if not args.keep_originals:
            print("  Original TIFF files have been deleted.")
        else:
            print("  Original TIFF files have been preserved.")
    
    if errors > 0:
        print(f"\n⚠ {errors} files could not be converted. Check the error messages above.")
        sys.exit(1)
    
    if converted == 0 and errors == 0:
        print("\nNo files to process.")

if __name__ == "__main__":
    main()
