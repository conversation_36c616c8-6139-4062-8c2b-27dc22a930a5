{"mask_name": "mask_00075.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"20": {"instance_id": 20, "class_name": "car", "x1": 1805, "y1": 260, "x2": 1919, "y2": 382, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1528, "y1": 18, "x2": 1571, "y2": 41, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "27": {"instance_id": 27, "class_name": "car", "x1": 1502, "y1": 10, "x2": 1542, "y2": 35, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1009, "y1": 50, "x2": 1040, "y2": 78, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 942, "y1": 16, "x2": 967, "y2": 34, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 951, "y1": 25, "x2": 974, "y2": 47, "logit": 0.0}, "28": {"instance_id": 28, "class_name": "car", "x1": 889, "y1": 6, "x2": 907, "y2": 24, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 922, "y1": 45, "x2": 953, "y2": 76, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1033, "y1": 32, "x2": 1060, "y2": 55, "logit": 0.0}}}