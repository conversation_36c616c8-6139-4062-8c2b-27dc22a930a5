{"mask_name": "mask_00114.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"13": {"instance_id": 13, "class_name": "car", "x1": 1041, "y1": 336, "x2": 1157, "y2": 473, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 899, "y1": 202, "x2": 981, "y2": 291, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1436, "y1": 116, "x2": 1522, "y2": 177, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1184, "y1": 227, "x2": 1282, "y2": 323, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1486, "y1": 4, "x2": 1531, "y2": 27, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 893, "y1": 108, "x2": 942, "y2": 156, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 842, "y1": 52, "x2": 880, "y2": 83, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 981, "y1": 65, "x2": 1024, "y2": 101, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1256, "y1": 0, "x2": 1286, "y2": 20, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1170, "y1": 17, "x2": 1187, "y2": 39, "logit": 0.0}, "35": {"instance_id": 35, "class_name": "car", "x1": 876, "y1": 0, "x2": 897, "y2": 27, "logit": 0.0}, "36": {"instance_id": 36, "class_name": "car", "x1": 1503, "y1": 10, "x2": 1543, "y2": 33, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1681, "y1": 27, "x2": 1736, "y2": 59, "logit": 0.0}, "38": {"instance_id": 38, "class_name": "car", "x1": 1709, "y1": 7, "x2": 1720, "y2": 23, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1135, "y1": 0, "x2": 1162, "y2": 22, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "40": {"instance_id": 40, "class_name": "car", "x1": 938, "y1": 0, "x2": 962, "y2": 18, "logit": 0.0}}}