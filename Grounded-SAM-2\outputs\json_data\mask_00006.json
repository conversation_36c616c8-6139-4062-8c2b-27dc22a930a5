{"mask_name": "mask_00006.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "car", "x1": 1119, "y1": 22, "x2": 1144, "y2": 42, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1100, "y1": 126, "x2": 1158, "y2": 172, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1020, "y1": 131, "x2": 1072, "y2": 182, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1709, "y1": 56, "x2": 1760, "y2": 81, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 972, "y1": 42, "x2": 998, "y2": 64, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 999, "y1": 71, "x2": 1037, "y2": 102, "logit": 0.0}, "7": {"instance_id": 7, "class_name": "car", "x1": 1255, "y1": 9, "x2": 1280, "y2": 22, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1636, "y1": 55, "x2": 1683, "y2": 80, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1342, "y1": 14, "x2": 1376, "y2": 34, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1026, "y1": 40, "x2": 1052, "y2": 61, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 914, "y1": 8, "x2": 930, "y2": 19, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1628, "y1": 48, "x2": 1654, "y2": 66, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 955, "y1": 7, "x2": 969, "y2": 16, "logit": 0.0}, "14": {"instance_id": 14, "class_name": "car", "x1": 1628, "y1": 47, "x2": 1663, "y2": 66, "logit": 0.0}, "15": {"instance_id": 15, "class_name": "car", "x1": 1167, "y1": 0, "x2": 1175, "y2": 9, "logit": 0.0}, "16": {"instance_id": 16, "class_name": "car", "x1": 991, "y1": 0, "x2": 1006, "y2": 7, "logit": 0.0}}}