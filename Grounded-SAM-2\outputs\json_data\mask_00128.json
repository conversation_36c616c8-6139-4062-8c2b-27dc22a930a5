{"mask_name": "mask_00128.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"31": {"instance_id": 31, "class_name": "car", "x1": 825, "y1": 207, "x2": 907, "y2": 290, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1837, "y1": 281, "x2": 1919, "y2": 385, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 842, "y1": 520, "x2": 1055, "y2": 808, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1001, "y1": 114, "x2": 1061, "y2": 166, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 812, "y1": 92, "x2": 863, "y2": 134, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1729, "y1": 31, "x2": 1788, "y2": 59, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1375, "y1": 0, "x2": 1414, "y2": 16, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1524, "y1": 12, "x2": 1568, "y2": 40, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 870, "y1": 19, "x2": 895, "y2": 44, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1209, "y1": 16, "x2": 1249, "y2": 44, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1211, "y1": 34, "x2": 1255, "y2": 64, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 956, "y1": 15, "x2": 978, "y2": 35, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 944, "y1": 3, "x2": 964, "y2": 21, "logit": 0.0}}}