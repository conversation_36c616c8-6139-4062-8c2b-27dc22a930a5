{"mask_name": "mask_016.npy", "mask_height": 240, "mask_width": 360, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "bike", "x1": 93, "y1": 129, "x2": 130, "y2": 147, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "bike", "x1": 93, "y1": 130, "x2": 130, "y2": 146, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "bike", "x1": 116, "y1": 134, "x2": 129, "y2": 146, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "bike", "x1": 95, "y1": 133, "x2": 129, "y2": 146, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "bike", "x1": 201, "y1": 225, "x2": 206, "y2": 238, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "bike", "x1": 193, "y1": 228, "x2": 202, "y2": 239, "logit": 0.0}}}