{"mask_name": "mask_00133.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"31": {"instance_id": 31, "class_name": "car", "x1": 769, "y1": 274, "x2": 879, "y2": 387, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 809, "y1": 929, "x2": 1077, "y2": 1079, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1011, "y1": 142, "x2": 1079, "y2": 206, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 794, "y1": 112, "x2": 853, "y2": 166, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1664, "y1": 25, "x2": 1714, "y2": 48, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1339, "y1": 0, "x2": 1381, "y2": 12, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1482, "y1": 7, "x2": 1522, "y2": 32, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 866, "y1": 29, "x2": 893, "y2": 52, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1245, "y1": 22, "x2": 1290, "y2": 50, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1240, "y1": 44, "x2": 1287, "y2": 77, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 961, "y1": 19, "x2": 986, "y2": 42, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 951, "y1": 8, "x2": 973, "y2": 26, "logit": 0.0}}}