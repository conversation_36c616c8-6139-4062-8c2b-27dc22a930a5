{"mask_name": "mask_00001.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "car", "x1": 1105, "y1": 21, "x2": 1129, "y2": 38, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1084, "y1": 108, "x2": 1133, "y2": 149, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1013, "y1": 112, "x2": 1060, "y2": 155, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1774, "y1": 67, "x2": 1828, "y2": 93, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 968, "y1": 40, "x2": 992, "y2": 60, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 991, "y1": 64, "x2": 1026, "y2": 92, "logit": 0.0}, "7": {"instance_id": 7, "class_name": "car", "x1": 1277, "y1": 13, "x2": 1300, "y2": 28, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1706, "y1": 66, "x2": 1756, "y2": 95, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1366, "y1": 18, "x2": 1401, "y2": 39, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1017, "y1": 38, "x2": 1041, "y2": 56, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 914, "y1": 9, "x2": 927, "y2": 19, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1681, "y1": 57, "x2": 1712, "y2": 78, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 950, "y1": 6, "x2": 962, "y2": 15, "logit": 0.0}, "14": {"instance_id": 14, "class_name": "car", "x1": 1680, "y1": 55, "x2": 1721, "y2": 79, "logit": 0.0}, "15": {"instance_id": 15, "class_name": "car", "x1": 1166, "y1": 0, "x2": 1179, "y2": 13, "logit": 0.0}, "16": {"instance_id": 16, "class_name": "car", "x1": 978, "y1": 0, "x2": 996, "y2": 8, "logit": 0.0}}}