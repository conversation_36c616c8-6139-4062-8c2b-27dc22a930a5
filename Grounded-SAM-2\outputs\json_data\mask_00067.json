{"mask_name": "mask_00067.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"20": {"instance_id": 20, "class_name": "car", "x1": 1560, "y1": 164, "x2": 1668, "y2": 243, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1639, "y1": 32, "x2": 1696, "y2": 61, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1435, "y1": 601, "x2": 1708, "y2": 913, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1020, "y1": 520, "x2": 1220, "y2": 781, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1130, "y1": 12, "x2": 1157, "y2": 33, "logit": 0.0}, "27": {"instance_id": 27, "class_name": "car", "x1": 1574, "y1": 20, "x2": 1622, "y2": 46, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1008, "y1": 35, "x2": 1035, "y2": 61, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 938, "y1": 10, "x2": 959, "y2": 29, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 955, "y1": 17, "x2": 977, "y2": 37, "logit": 0.0}, "28": {"instance_id": 28, "class_name": "car", "x1": 893, "y1": 3, "x2": 911, "y2": 19, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 924, "y1": 32, "x2": 955, "y2": 62, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1021, "y1": 24, "x2": 1045, "y2": 43, "logit": 0.0}}}