{"mask_name": "mask_00015.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "car", "x1": 1149, "y1": 29, "x2": 1169, "y2": 49, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1149, "y1": 182, "x2": 1229, "y2": 248, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1035, "y1": 191, "x2": 1105, "y2": 258, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1611, "y1": 40, "x2": 1656, "y2": 61, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 984, "y1": 52, "x2": 1014, "y2": 78, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1013, "y1": 91, "x2": 1058, "y2": 129, "logit": 0.0}, "7": {"instance_id": 7, "class_name": "car", "x1": 1225, "y1": 0, "x2": 1247, "y2": 14, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1536, "y1": 36, "x2": 1583, "y2": 62, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1307, "y1": 3, "x2": 1335, "y2": 25, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1047, "y1": 49, "x2": 1076, "y2": 73, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 925, "y1": 7, "x2": 941, "y2": 21, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1562, "y1": 33, "x2": 1587, "y2": 51, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 968, "y1": 0, "x2": 984, "y2": 17, "logit": 0.0}, "14": {"instance_id": 14, "class_name": "car", "x1": 1541, "y1": 32, "x2": 1588, "y2": 52, "logit": 0.0}, "15": {"instance_id": 15, "class_name": "car", "x1": 1171, "y1": 0, "x2": 1179, "y2": 15, "logit": 0.0}, "16": {"instance_id": 16, "class_name": "car", "x1": 1009, "y1": 0, "x2": 1023, "y2": 7, "logit": 0.0}}}