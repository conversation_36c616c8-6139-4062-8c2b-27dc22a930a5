{"mask_name": "mask_00124.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"31": {"instance_id": 31, "class_name": "car", "x1": 855, "y1": 165, "x2": 925, "y2": 234, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1662, "y1": 205, "x2": 1806, "y2": 300, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1407, "y1": 587, "x2": 1670, "y2": 882, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1108, "y1": 1029, "x2": 1329, "y2": 1079, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 873, "y1": 363, "x2": 1024, "y2": 542, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 995, "y1": 94, "x2": 1050, "y2": 140, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 826, "y1": 76, "x2": 871, "y2": 116, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1796, "y1": 36, "x2": 1860, "y2": 67, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1409, "y1": 0, "x2": 1446, "y2": 19, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1570, "y1": 15, "x2": 1614, "y2": 45, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 874, "y1": 14, "x2": 897, "y2": 37, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1183, "y1": 10, "x2": 1220, "y2": 36, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1192, "y1": 27, "x2": 1231, "y2": 53, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 952, "y1": 10, "x2": 973, "y2": 29, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 939, "y1": 0, "x2": 958, "y2": 17, "logit": 0.0}}}