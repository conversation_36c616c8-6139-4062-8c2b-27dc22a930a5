{"mask_name": "mask_00123.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"31": {"instance_id": 31, "class_name": "car", "x1": 858, "y1": 161, "x2": 925, "y2": 228, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1642, "y1": 199, "x2": 1779, "y2": 287, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1379, "y1": 542, "x2": 1619, "y2": 805, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1087, "y1": 926, "x2": 1337, "y2": 1079, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 874, "y1": 346, "x2": 1020, "y2": 519, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 995, "y1": 92, "x2": 1047, "y2": 138, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 827, "y1": 75, "x2": 871, "y2": 114, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1804, "y1": 38, "x2": 1870, "y2": 69, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1412, "y1": 0, "x2": 1452, "y2": 21, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1578, "y1": 16, "x2": 1620, "y2": 46, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 874, "y1": 14, "x2": 897, "y2": 37, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1181, "y1": 9, "x2": 1215, "y2": 36, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1187, "y1": 26, "x2": 1227, "y2": 53, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 950, "y1": 10, "x2": 972, "y2": 29, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 939, "y1": 0, "x2": 958, "y2": 16, "logit": 0.0}}}