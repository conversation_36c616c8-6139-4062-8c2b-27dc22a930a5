{"mask_name": "mask_00037.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"6": {"instance_id": 6, "class_name": "car", "x1": 1043, "y1": 219, "x2": 1133, "y2": 301, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1002, "y1": 98, "x2": 1047, "y2": 138, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1103, "y1": 95, "x2": 1151, "y2": 137, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1426, "y1": 10, "x2": 1459, "y2": 27, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 930, "y1": 12, "x2": 950, "y2": 30, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 993, "y1": 9, "x2": 1012, "y2": 26, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1384, "y1": 5, "x2": 1413, "y2": 21, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1347, "y1": 7, "x2": 1376, "y2": 25, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 910, "y1": 0, "x2": 930, "y2": 14, "logit": 0.0}, "18": {"instance_id": 18, "class_name": "car", "x1": 981, "y1": 0, "x2": 996, "y2": 17, "logit": 0.0}, "19": {"instance_id": 19, "class_name": "car", "x1": 949, "y1": 0, "x2": 964, "y2": 15, "logit": 0.0}}}