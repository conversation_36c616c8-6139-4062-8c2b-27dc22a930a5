{"mask_name": "mask_00041.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"10": {"instance_id": 10, "class_name": "car", "x1": 1117, "y1": 111, "x2": 1170, "y2": 160, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1005, "y1": 115, "x2": 1056, "y2": 161, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1051, "y1": 279, "x2": 1163, "y2": 386, "logit": 0.0}, "20": {"instance_id": 20, "class_name": "car", "x1": 1260, "y1": 56, "x2": 1305, "y2": 88, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 931, "y1": 14, "x2": 951, "y2": 32, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1321, "y1": 4, "x2": 1347, "y2": 20, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1362, "y1": 3, "x2": 1388, "y2": 18, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 951, "y1": 5, "x2": 967, "y2": 17, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 996, "y1": 14, "x2": 1015, "y2": 29, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1399, "y1": 5, "x2": 1429, "y2": 22, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 987, "y1": 5, "x2": 1005, "y2": 19, "logit": 0.0}, "23": {"instance_id": 23, "class_name": "car", "x1": 1146, "y1": 68, "x2": 1179, "y2": 94, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 916, "y1": 0, "x2": 935, "y2": 14, "logit": 0.0}, "24": {"instance_id": 24, "class_name": "car", "x1": 896, "y1": 0, "x2": 910, "y2": 7, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1067, "y1": 0, "x2": 1088, "y2": 14, "logit": 0.0}}}