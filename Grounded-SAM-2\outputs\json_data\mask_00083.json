{"mask_name": "mask_00083.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"22": {"instance_id": 22, "class_name": "car", "x1": 1045, "y1": 47, "x2": 1079, "y2": 75, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1010, "y1": 67, "x2": 1046, "y2": 103, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1571, "y1": 53, "x2": 1630, "y2": 87, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1429, "y1": 3, "x2": 1470, "y2": 28, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1188, "y1": 27, "x2": 1223, "y2": 52, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 914, "y1": 59, "x2": 951, "y2": 96, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 880, "y1": 3, "x2": 903, "y2": 32, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 941, "y1": 23, "x2": 977, "y2": 59, "logit": 0.0}, "28": {"instance_id": 28, "class_name": "car", "x1": 880, "y1": 13, "x2": 903, "y2": 32, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 951, "y1": 22, "x2": 976, "y2": 39, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1071, "y1": 0, "x2": 1095, "y2": 12, "logit": 0.0}, "33": {"instance_id": 33, "class_name": "car", "x1": 880, "y1": 14, "x2": 903, "y2": 32, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 940, "y1": 33, "x2": 968, "y2": 60, "logit": 0.0}}}