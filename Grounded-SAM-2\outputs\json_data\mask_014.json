{"mask_name": "mask_014.npy", "mask_height": 240, "mask_width": 360, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "bike", "x1": 91, "y1": 128, "x2": 126, "y2": 146, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "bike", "x1": 91, "y1": 128, "x2": 126, "y2": 146, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "bike", "x1": 112, "y1": 134, "x2": 126, "y2": 146, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "bike", "x1": 92, "y1": 129, "x2": 126, "y2": 146, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "bike", "x1": 201, "y1": 228, "x2": 205, "y2": 239, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "bike", "x1": 192, "y1": 230, "x2": 201, "y2": 239, "logit": 0.0}}}