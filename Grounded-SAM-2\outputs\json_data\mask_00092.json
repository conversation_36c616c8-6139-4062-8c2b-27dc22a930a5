{"mask_name": "mask_00092.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"22": {"instance_id": 22, "class_name": "car", "x1": 1063, "y1": 68, "x2": 1104, "y2": 103, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1013, "y1": 98, "x2": 1059, "y2": 140, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1449, "y1": 29, "x2": 1496, "y2": 59, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1346, "y1": 0, "x2": 1405, "y2": 17, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1229, "y1": 41, "x2": 1271, "y2": 71, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 909, "y1": 77, "x2": 951, "y2": 124, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 871, "y1": 14, "x2": 897, "y2": 42, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 930, "y1": 48, "x2": 959, "y2": 74, "logit": 0.0}, "28": {"instance_id": 28, "class_name": "car", "x1": 871, "y1": 18, "x2": 897, "y2": 42, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 957, "y1": 29, "x2": 986, "y2": 51, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1088, "y1": 0, "x2": 1116, "y2": 16, "logit": 0.0}, "33": {"instance_id": 33, "class_name": "car", "x1": 871, "y1": 18, "x2": 897, "y2": 42, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 930, "y1": 48, "x2": 959, "y2": 75, "logit": 0.0}}}