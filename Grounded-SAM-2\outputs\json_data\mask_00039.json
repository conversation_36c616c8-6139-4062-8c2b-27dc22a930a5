{"mask_name": "mask_00039.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"6": {"instance_id": 6, "class_name": "car", "x1": 1046, "y1": 240, "x2": 1146, "y2": 336, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1004, "y1": 104, "x2": 1052, "y2": 147, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1109, "y1": 102, "x2": 1159, "y2": 146, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1413, "y1": 8, "x2": 1444, "y2": 24, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 930, "y1": 13, "x2": 951, "y2": 31, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 993, "y1": 10, "x2": 1014, "y2": 27, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1376, "y1": 5, "x2": 1403, "y2": 19, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1336, "y1": 5, "x2": 1364, "y2": 23, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 913, "y1": 0, "x2": 932, "y2": 14, "logit": 0.0}, "18": {"instance_id": 18, "class_name": "car", "x1": 985, "y1": 3, "x2": 999, "y2": 18, "logit": 0.0}, "19": {"instance_id": 19, "class_name": "car", "x1": 951, "y1": 4, "x2": 965, "y2": 16, "logit": 0.0}}}