{"mask_name": "mask_00060.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"10": {"instance_id": 10, "class_name": "car", "x1": 1254, "y1": 304, "x2": 1382, "y2": 432, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1014, "y1": 289, "x2": 1128, "y2": 412, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "20": {"instance_id": 20, "class_name": "car", "x1": 1431, "y1": 118, "x2": 1509, "y2": 173, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 927, "y1": 27, "x2": 955, "y2": 53, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1215, "y1": 0, "x2": 1234, "y2": 4, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 955, "y1": 13, "x2": 976, "y2": 31, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1005, "y1": 25, "x2": 1031, "y2": 51, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1009, "y1": 17, "x2": 1031, "y2": 31, "logit": 0.0}, "23": {"instance_id": 23, "class_name": "car", "x1": 1143, "y1": 65, "x2": 1178, "y2": 92, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 929, "y1": 9, "x2": 952, "y2": 26, "logit": 0.0}, "24": {"instance_id": 24, "class_name": "car", "x1": 895, "y1": 0, "x2": 911, "y2": 15, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1110, "y1": 7, "x2": 1137, "y2": 26, "logit": 0.0}}}