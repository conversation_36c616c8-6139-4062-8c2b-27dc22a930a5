{"mask_name": "mask_00117.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"13": {"instance_id": 13, "class_name": "car", "x1": 1050, "y1": 436, "x2": 1198, "y2": 623, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 895, "y1": 238, "x2": 989, "y2": 344, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1491, "y1": 136, "x2": 1587, "y2": 205, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1225, "y1": 289, "x2": 1347, "y2": 410, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1459, "y1": 2, "x2": 1505, "y2": 27, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 884, "y1": 122, "x2": 938, "y2": 175, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 837, "y1": 61, "x2": 877, "y2": 93, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 985, "y1": 73, "x2": 1031, "y2": 113, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1237, "y1": 0, "x2": 1265, "y2": 16, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1171, "y1": 16, "x2": 1199, "y2": 43, "logit": 0.0}, "35": {"instance_id": 35, "class_name": "car", "x1": 875, "y1": 0, "x2": 897, "y2": 31, "logit": 0.0}, "36": {"instance_id": 36, "class_name": "car", "x1": 1470, "y1": 6, "x2": 1509, "y2": 29, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1643, "y1": 22, "x2": 1694, "y2": 54, "logit": 0.0}, "38": {"instance_id": 38, "class_name": "car", "x1": 1704, "y1": 7, "x2": 1720, "y2": 23, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "40": {"instance_id": 40, "class_name": "car", "x1": 944, "y1": 0, "x2": 965, "y2": 22, "logit": 0.0}}}