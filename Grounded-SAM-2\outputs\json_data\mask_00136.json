{"mask_name": "mask_00136.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"31": {"instance_id": 31, "class_name": "car", "x1": 713, "y1": 336, "x2": 849, "y2": 483, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1017, "y1": 162, "x2": 1092, "y2": 232, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 775, "y1": 129, "x2": 842, "y2": 187, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1627, "y1": 19, "x2": 1675, "y2": 43, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1318, "y1": 0, "x2": 1354, "y2": 7, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1457, "y1": 3, "x2": 1497, "y2": 29, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 864, "y1": 30, "x2": 892, "y2": 56, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1272, "y1": 28, "x2": 1319, "y2": 62, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1259, "y1": 50, "x2": 1312, "y2": 85, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 964, "y1": 22, "x2": 990, "y2": 45, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 955, "y1": 10, "x2": 976, "y2": 29, "logit": 0.0}}}