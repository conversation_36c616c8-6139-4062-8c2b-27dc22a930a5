{"mask_name": "mask_00023.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"6": {"instance_id": 6, "class_name": "car", "x1": 1025, "y1": 120, "x2": 1080, "y2": 169, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1052, "y1": 299, "x2": 1156, "y2": 412, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1222, "y1": 287, "x2": 1344, "y2": 394, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 993, "y1": 65, "x2": 1027, "y2": 95, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1067, "y1": 63, "x2": 1103, "y2": 91, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1535, "y1": 27, "x2": 1574, "y2": 47, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 928, "y1": 9, "x2": 946, "y2": 23, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 981, "y1": 5, "x2": 996, "y2": 19, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1267, "y1": 0, "x2": 1299, "y2": 18, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1488, "y1": 19, "x2": 1516, "y2": 39, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1456, "y1": 24, "x2": 1491, "y2": 45, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 902, "y1": 3, "x2": 920, "y2": 12, "logit": 0.0}, "18": {"instance_id": 18, "class_name": "car", "x1": 966, "y1": 0, "x2": 982, "y2": 13, "logit": 0.0}, "19": {"instance_id": 19, "class_name": "car", "x1": 945, "y1": 0, "x2": 956, "y2": 11, "logit": 0.0}}}