{"mask_name": "mask_00087.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"22": {"instance_id": 22, "class_name": "car", "x1": 1052, "y1": 55, "x2": 1088, "y2": 85, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1011, "y1": 80, "x2": 1053, "y2": 116, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1517, "y1": 39, "x2": 1571, "y2": 74, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1393, "y1": 0, "x2": 1443, "y2": 24, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1202, "y1": 33, "x2": 1240, "y2": 59, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 911, "y1": 66, "x2": 950, "y2": 105, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 877, "y1": 5, "x2": 900, "y2": 36, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 937, "y1": 35, "x2": 965, "y2": 66, "logit": 0.0}, "28": {"instance_id": 28, "class_name": "car", "x1": 876, "y1": 15, "x2": 900, "y2": 36, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 954, "y1": 25, "x2": 980, "y2": 45, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1077, "y1": 0, "x2": 1103, "y2": 14, "logit": 0.0}, "33": {"instance_id": 33, "class_name": "car", "x1": 877, "y1": 15, "x2": 900, "y2": 36, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 937, "y1": 38, "x2": 966, "y2": 66, "logit": 0.0}}}