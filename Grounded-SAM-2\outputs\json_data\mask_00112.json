{"mask_name": "mask_00112.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"13": {"instance_id": 13, "class_name": "car", "x1": 1036, "y1": 280, "x2": 1135, "y2": 391, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 900, "y1": 179, "x2": 975, "y2": 258, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1401, "y1": 103, "x2": 1477, "y2": 157, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1159, "y1": 193, "x2": 1242, "y2": 272, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1506, "y1": 7, "x2": 1550, "y2": 29, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 897, "y1": 98, "x2": 944, "y2": 139, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 848, "y1": 48, "x2": 882, "y2": 77, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 978, "y1": 60, "x2": 1019, "y2": 94, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1272, "y1": 0, "x2": 1303, "y2": 23, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "35": {"instance_id": 35, "class_name": "car", "x1": 877, "y1": 0, "x2": 898, "y2": 26, "logit": 0.0}, "36": {"instance_id": 36, "class_name": "car", "x1": 1533, "y1": 14, "x2": 1571, "y2": 38, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1714, "y1": 32, "x2": 1773, "y2": 64, "logit": 0.0}, "38": {"instance_id": 38, "class_name": "car", "x1": 1709, "y1": 7, "x2": 1721, "y2": 23, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1124, "y1": 0, "x2": 1151, "y2": 20, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "40": {"instance_id": 40, "class_name": "car", "x1": 939, "y1": 0, "x2": 959, "y2": 17, "logit": 0.0}}}