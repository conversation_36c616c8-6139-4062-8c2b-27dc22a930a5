"""
Example configuration showing how to use the new output folder features
in grounded_sam2_tracking_demo_custom_video_input_gd1.0_hf_model.py

To enable output features, modify the configuration variables at the top of the main script:
"""

# Example 1: Enable output folder with both video and contours
OUTPUT_FOLDER = "./output"  # Specify output folder path
SAVE_CONTOURS = True        # Enable contour extraction and saving

# Example 2: Enable output folder with video only (no contours)
# OUTPUT_FOLDER = "./output"
# SAVE_CONTOURS = False

# Example 3: Disable output features (original behavior)
# OUTPUT_FOLDER = None
# SAVE_CONTOURS = True  # This setting is ignored when OUTPUT_FOLDER is None

"""
When OUTPUT_FOLDER is specified, the script will create:

output/
├── processed_videos/
│   └── processed_[original_video_name].mp4  # Video with segmentation overlays
└── contours/
    ├── contours_data.json                   # All contours in one file
    ├── frame_00000_contours.json           # Individual frame contours
    ├── frame_00001_contours.json
    └── ...

Contour data format:
{
  "frame_00000": {
    "object_1": {
      "label": "yellow ball",
      "contours": [
        [[x1, y1], [x2, y2], ...],  # First contour points
        [[x3, y3], [x4, y4], ...]   # Second contour points (if multiple)
      ]
    }
  }
}
"""
