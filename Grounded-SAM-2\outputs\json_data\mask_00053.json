{"mask_name": "mask_00053.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"10": {"instance_id": 10, "class_name": "car", "x1": 1182, "y1": 199, "x2": 1268, "y2": 277, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1010, "y1": 197, "x2": 1089, "y2": 274, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1106, "y1": 811, "x2": 1438, "y2": 1079, "logit": 0.0}, "20": {"instance_id": 20, "class_name": "car", "x1": 1352, "y1": 87, "x2": 1416, "y2": 133, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 929, "y1": 21, "x2": 954, "y2": 45, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1249, "y1": 0, "x2": 1271, "y2": 9, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1299, "y1": 0, "x2": 1318, "y2": 11, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 954, "y1": 6, "x2": 975, "y2": 26, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1001, "y1": 16, "x2": 1025, "y2": 42, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1317, "y1": 0, "x2": 1345, "y2": 13, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1001, "y1": 13, "x2": 1019, "y2": 27, "logit": 0.0}, "23": {"instance_id": 23, "class_name": "car", "x1": 1144, "y1": 65, "x2": 1177, "y2": 94, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 925, "y1": 4, "x2": 945, "y2": 22, "logit": 0.0}, "24": {"instance_id": 24, "class_name": "car", "x1": 896, "y1": 0, "x2": 911, "y2": 12, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1093, "y1": 3, "x2": 1118, "y2": 21, "logit": 0.0}}}