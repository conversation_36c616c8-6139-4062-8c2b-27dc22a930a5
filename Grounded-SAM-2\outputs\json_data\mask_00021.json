{"mask_name": "mask_00021.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"6": {"instance_id": 6, "class_name": "car", "x1": 1021, "y1": 110, "x2": 1073, "y2": 154, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1048, "y1": 258, "x2": 1138, "y2": 353, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1196, "y1": 247, "x2": 1300, "y2": 336, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 991, "y1": 61, "x2": 1025, "y2": 89, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1061, "y1": 59, "x2": 1092, "y2": 85, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1557, "y1": 30, "x2": 1597, "y2": 50, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 928, "y1": 8, "x2": 945, "y2": 22, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 979, "y1": 5, "x2": 995, "y2": 19, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1281, "y1": 0, "x2": 1313, "y2": 19, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1510, "y1": 25, "x2": 1535, "y2": 41, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1479, "y1": 27, "x2": 1515, "y2": 49, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 905, "y1": 4, "x2": 918, "y2": 12, "logit": 0.0}, "18": {"instance_id": 18, "class_name": "car", "x1": 964, "y1": 0, "x2": 980, "y2": 11, "logit": 0.0}, "19": {"instance_id": 19, "class_name": "car", "x1": 943, "y1": 0, "x2": 955, "y2": 11, "logit": 0.0}}}