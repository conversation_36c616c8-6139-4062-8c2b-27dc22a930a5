{"mask_name": "mask_00081.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"22": {"instance_id": 22, "class_name": "car", "x1": 1043, "y1": 42, "x2": 1073, "y2": 67, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1011, "y1": 64, "x2": 1045, "y2": 94, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1612, "y1": 60, "x2": 1675, "y2": 95, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1451, "y1": 5, "x2": 1494, "y2": 31, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1185, "y1": 23, "x2": 1213, "y2": 48, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 916, "y1": 54, "x2": 951, "y2": 88, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 885, "y1": 0, "x2": 906, "y2": 29, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 946, "y1": 20, "x2": 974, "y2": 54, "logit": 0.0}, "28": {"instance_id": 28, "class_name": "car", "x1": 885, "y1": 12, "x2": 906, "y2": 29, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 950, "y1": 19, "x2": 975, "y2": 33, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1069, "y1": 0, "x2": 1090, "y2": 9, "logit": 0.0}, "33": {"instance_id": 33, "class_name": "car", "x1": 886, "y1": 13, "x2": 906, "y2": 29, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 946, "y1": 31, "x2": 973, "y2": 54, "logit": 0.0}}}