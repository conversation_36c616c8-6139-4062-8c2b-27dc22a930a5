{"mask_name": "mask_00113.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"13": {"instance_id": 13, "class_name": "car", "x1": 1038, "y1": 306, "x2": 1146, "y2": 430, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 900, "y1": 190, "x2": 979, "y2": 275, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1418, "y1": 110, "x2": 1500, "y2": 166, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1171, "y1": 209, "x2": 1262, "y2": 297, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1499, "y1": 6, "x2": 1536, "y2": 27, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 894, "y1": 102, "x2": 944, "y2": 147, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 845, "y1": 50, "x2": 882, "y2": 81, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 980, "y1": 62, "x2": 1023, "y2": 97, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1264, "y1": 0, "x2": 1295, "y2": 23, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1158, "y1": 10, "x2": 1183, "y2": 33, "logit": 0.0}, "35": {"instance_id": 35, "class_name": "car", "x1": 878, "y1": 0, "x2": 898, "y2": 27, "logit": 0.0}, "36": {"instance_id": 36, "class_name": "car", "x1": 1520, "y1": 11, "x2": 1558, "y2": 36, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1698, "y1": 30, "x2": 1757, "y2": 60, "logit": 0.0}, "38": {"instance_id": 38, "class_name": "car", "x1": 1710, "y1": 7, "x2": 1721, "y2": 26, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1128, "y1": 0, "x2": 1157, "y2": 22, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "40": {"instance_id": 40, "class_name": "car", "x1": 940, "y1": 0, "x2": 961, "y2": 18, "logit": 0.0}}}