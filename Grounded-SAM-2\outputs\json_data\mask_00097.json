{"mask_name": "mask_00097.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"22": {"instance_id": 22, "class_name": "car", "x1": 1077, "y1": 84, "x2": 1124, "y2": 126, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1016, "y1": 123, "x2": 1068, "y2": 173, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1397, "y1": 22, "x2": 1439, "y2": 47, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1309, "y1": 0, "x2": 1375, "y2": 13, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1258, "y1": 51, "x2": 1307, "y2": 85, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 907, "y1": 95, "x2": 954, "y2": 142, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 865, "y1": 23, "x2": 895, "y2": 49, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 922, "y1": 55, "x2": 956, "y2": 87, "logit": 0.0}, "28": {"instance_id": 28, "class_name": "car", "x1": 866, "y1": 25, "x2": 894, "y2": 48, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 961, "y1": 33, "x2": 994, "y2": 59, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1099, "y1": 0, "x2": 1127, "y2": 20, "logit": 0.0}, "33": {"instance_id": 33, "class_name": "car", "x1": 866, "y1": 25, "x2": 894, "y2": 48, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 923, "y1": 55, "x2": 957, "y2": 87, "logit": 0.0}}}