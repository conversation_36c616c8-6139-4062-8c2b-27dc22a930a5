{"mask_name": "mask_00085.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"22": {"instance_id": 22, "class_name": "car", "x1": 1049, "y1": 50, "x2": 1083, "y2": 80, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1011, "y1": 74, "x2": 1048, "y2": 110, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1543, "y1": 47, "x2": 1599, "y2": 81, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1411, "y1": 0, "x2": 1455, "y2": 27, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1193, "y1": 31, "x2": 1231, "y2": 56, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 913, "y1": 62, "x2": 950, "y2": 100, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 878, "y1": 4, "x2": 902, "y2": 34, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 940, "y1": 24, "x2": 979, "y2": 62, "logit": 0.0}, "28": {"instance_id": 28, "class_name": "car", "x1": 877, "y1": 14, "x2": 902, "y2": 34, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 953, "y1": 24, "x2": 978, "y2": 43, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1073, "y1": 0, "x2": 1099, "y2": 13, "logit": 0.0}, "33": {"instance_id": 33, "class_name": "car", "x1": 877, "y1": 15, "x2": 902, "y2": 34, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 939, "y1": 35, "x2": 968, "y2": 62, "logit": 0.0}}}