{"mask_name": "mask_00025.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"6": {"instance_id": 6, "class_name": "car", "x1": 1027, "y1": 128, "x2": 1085, "y2": 179, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1056, "y1": 338, "x2": 1174, "y2": 465, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1245, "y1": 324, "x2": 1383, "y2": 450, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 993, "y1": 67, "x2": 1030, "y2": 99, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1070, "y1": 66, "x2": 1106, "y2": 95, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1519, "y1": 24, "x2": 1556, "y2": 44, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 927, "y1": 9, "x2": 946, "y2": 24, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 982, "y1": 7, "x2": 999, "y2": 20, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1261, "y1": 0, "x2": 1291, "y2": 16, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1473, "y1": 17, "x2": 1500, "y2": 35, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1441, "y1": 21, "x2": 1473, "y2": 41, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 903, "y1": 3, "x2": 921, "y2": 13, "logit": 0.0}, "18": {"instance_id": 18, "class_name": "car", "x1": 968, "y1": 0, "x2": 984, "y2": 14, "logit": 0.0}, "19": {"instance_id": 19, "class_name": "car", "x1": 946, "y1": 0, "x2": 956, "y2": 12, "logit": 0.0}}}