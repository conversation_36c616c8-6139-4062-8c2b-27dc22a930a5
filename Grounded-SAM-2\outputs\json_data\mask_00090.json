{"mask_name": "mask_00090.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"22": {"instance_id": 22, "class_name": "car", "x1": 1058, "y1": 62, "x2": 1095, "y2": 95, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1012, "y1": 88, "x2": 1054, "y2": 128, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1477, "y1": 35, "x2": 1527, "y2": 65, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1366, "y1": 0, "x2": 1421, "y2": 20, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1216, "y1": 37, "x2": 1257, "y2": 65, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 909, "y1": 72, "x2": 950, "y2": 115, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 873, "y1": 10, "x2": 897, "y2": 39, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 932, "y1": 42, "x2": 961, "y2": 70, "logit": 0.0}, "28": {"instance_id": 28, "class_name": "car", "x1": 873, "y1": 16, "x2": 897, "y2": 39, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 955, "y1": 27, "x2": 983, "y2": 49, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1082, "y1": 0, "x2": 1109, "y2": 15, "logit": 0.0}, "33": {"instance_id": 33, "class_name": "car", "x1": 873, "y1": 16, "x2": 897, "y2": 39, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 932, "y1": 43, "x2": 960, "y2": 71, "logit": 0.0}}}