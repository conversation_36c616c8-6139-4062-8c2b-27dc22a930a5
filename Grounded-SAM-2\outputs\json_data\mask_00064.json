{"mask_name": "mask_00064.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"20": {"instance_id": 20, "class_name": "car", "x1": 1499, "y1": 139, "x2": 1593, "y2": 209, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1689, "y1": 40, "x2": 1750, "y2": 68, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1336, "y1": 433, "x2": 1523, "y2": 631, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1015, "y1": 396, "x2": 1168, "y2": 572, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1120, "y1": 10, "x2": 1150, "y2": 30, "logit": 0.0}, "27": {"instance_id": 27, "class_name": "car", "x1": 1608, "y1": 24, "x2": 1658, "y2": 52, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1007, "y1": 32, "x2": 1034, "y2": 57, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 935, "y1": 9, "x2": 958, "y2": 28, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 957, "y1": 16, "x2": 979, "y2": 35, "logit": 0.0}, "28": {"instance_id": 28, "class_name": "car", "x1": 894, "y1": 3, "x2": 912, "y2": 18, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 925, "y1": 31, "x2": 956, "y2": 59, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1017, "y1": 20, "x2": 1040, "y2": 39, "logit": 0.0}}}