{"mask_name": "mask_00093.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"22": {"instance_id": 22, "class_name": "car", "x1": 1065, "y1": 68, "x2": 1107, "y2": 106, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1015, "y1": 100, "x2": 1059, "y2": 144, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1442, "y1": 30, "x2": 1489, "y2": 57, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1341, "y1": 0, "x2": 1401, "y2": 17, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1233, "y1": 42, "x2": 1278, "y2": 73, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 909, "y1": 78, "x2": 950, "y2": 125, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 871, "y1": 13, "x2": 897, "y2": 43, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 928, "y1": 48, "x2": 958, "y2": 76, "logit": 0.0}, "28": {"instance_id": 28, "class_name": "car", "x1": 871, "y1": 19, "x2": 896, "y2": 42, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 957, "y1": 29, "x2": 987, "y2": 53, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1088, "y1": 0, "x2": 1117, "y2": 17, "logit": 0.0}, "33": {"instance_id": 33, "class_name": "car", "x1": 871, "y1": 20, "x2": 896, "y2": 42, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 928, "y1": 48, "x2": 959, "y2": 77, "logit": 0.0}}}