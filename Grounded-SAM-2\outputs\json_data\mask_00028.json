{"mask_name": "mask_00028.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"6": {"instance_id": 6, "class_name": "car", "x1": 1030, "y1": 141, "x2": 1094, "y2": 202, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1063, "y1": 425, "x2": 1213, "y2": 605, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1293, "y1": 411, "x2": 1471, "y2": 575, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 996, "y1": 75, "x2": 1034, "y2": 107, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1077, "y1": 72, "x2": 1114, "y2": 103, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1496, "y1": 21, "x2": 1530, "y2": 38, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 927, "y1": 10, "x2": 947, "y2": 26, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 984, "y1": 9, "x2": 1003, "y2": 22, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1248, "y1": 0, "x2": 1276, "y2": 14, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1446, "y1": 16, "x2": 1475, "y2": 32, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1415, "y1": 17, "x2": 1447, "y2": 36, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 907, "y1": 3, "x2": 925, "y2": 13, "logit": 0.0}, "18": {"instance_id": 18, "class_name": "car", "x1": 971, "y1": 0, "x2": 986, "y2": 14, "logit": 0.0}, "19": {"instance_id": 19, "class_name": "car", "x1": 947, "y1": 0, "x2": 956, "y2": 13, "logit": 0.0}}}