{"mask_name": "mask_00099.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"22": {"instance_id": 22, "class_name": "car", "x1": 1085, "y1": 94, "x2": 1136, "y2": 139, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1019, "y1": 137, "x2": 1074, "y2": 192, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1374, "y1": 17, "x2": 1413, "y2": 41, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1326, "y1": 0, "x2": 1359, "y2": 10, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1275, "y1": 55, "x2": 1326, "y2": 93, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 907, "y1": 103, "x2": 956, "y2": 155, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 864, "y1": 28, "x2": 893, "y2": 52, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 919, "y1": 61, "x2": 954, "y2": 94, "logit": 0.0}, "28": {"instance_id": 28, "class_name": "car", "x1": 864, "y1": 29, "x2": 893, "y2": 51, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 964, "y1": 36, "x2": 998, "y2": 64, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1105, "y1": 0, "x2": 1134, "y2": 22, "logit": 0.0}, "33": {"instance_id": 33, "class_name": "car", "x1": 864, "y1": 29, "x2": 893, "y2": 51, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 920, "y1": 61, "x2": 956, "y2": 94, "logit": 0.0}}}