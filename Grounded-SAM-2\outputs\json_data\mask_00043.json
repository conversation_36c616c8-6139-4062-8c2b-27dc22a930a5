{"mask_name": "mask_00043.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"10": {"instance_id": 10, "class_name": "car", "x1": 1124, "y1": 120, "x2": 1181, "y2": 172, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1005, "y1": 125, "x2": 1059, "y2": 175, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1052, "y1": 314, "x2": 1180, "y2": 437, "logit": 0.0}, "20": {"instance_id": 20, "class_name": "car", "x1": 1272, "y1": 60, "x2": 1318, "y2": 95, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 930, "y1": 15, "x2": 952, "y2": 35, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1311, "y1": 0, "x2": 1335, "y2": 19, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1349, "y1": 0, "x2": 1378, "y2": 17, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 950, "y1": 5, "x2": 967, "y2": 20, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 998, "y1": 15, "x2": 1018, "y2": 32, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1385, "y1": 4, "x2": 1416, "y2": 21, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 988, "y1": 4, "x2": 1007, "y2": 21, "logit": 0.0}, "23": {"instance_id": 23, "class_name": "car", "x1": 1145, "y1": 67, "x2": 1180, "y2": 95, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 916, "y1": 2, "x2": 936, "y2": 17, "logit": 0.0}, "24": {"instance_id": 24, "class_name": "car", "x1": 895, "y1": 0, "x2": 909, "y2": 9, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1070, "y1": 0, "x2": 1092, "y2": 16, "logit": 0.0}}}