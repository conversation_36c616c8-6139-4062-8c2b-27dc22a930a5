{"mask_name": "mask_00031.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"6": {"instance_id": 6, "class_name": "car", "x1": 1034, "y1": 163, "x2": 1105, "y2": 226, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1073, "y1": 562, "x2": 1272, "y2": 817, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1368, "y1": 542, "x2": 1616, "y2": 797, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 998, "y1": 81, "x2": 1039, "y2": 116, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1085, "y1": 80, "x2": 1128, "y2": 113, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1471, "y1": 17, "x2": 1505, "y2": 35, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 928, "y1": 11, "x2": 948, "y2": 27, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 987, "y1": 8, "x2": 1007, "y2": 23, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1237, "y1": 0, "x2": 1263, "y2": 10, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1423, "y1": 11, "x2": 1456, "y2": 28, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1391, "y1": 14, "x2": 1423, "y2": 32, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 908, "y1": 2, "x2": 927, "y2": 13, "logit": 0.0}, "18": {"instance_id": 18, "class_name": "car", "x1": 975, "y1": 0, "x2": 990, "y2": 15, "logit": 0.0}, "19": {"instance_id": 19, "class_name": "car", "x1": 947, "y1": 0, "x2": 957, "y2": 12, "logit": 0.0}}}