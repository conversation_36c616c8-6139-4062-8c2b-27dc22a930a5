{"mask_name": "mask_00030.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"6": {"instance_id": 6, "class_name": "car", "x1": 1033, "y1": 156, "x2": 1101, "y2": 217, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1068, "y1": 498, "x2": 1245, "y2": 714, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1333, "y1": 482, "x2": 1549, "y2": 694, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 998, "y1": 78, "x2": 1037, "y2": 113, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1083, "y1": 77, "x2": 1122, "y2": 109, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1480, "y1": 18, "x2": 1516, "y2": 36, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 927, "y1": 11, "x2": 948, "y2": 27, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 986, "y1": 9, "x2": 1006, "y2": 23, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1241, "y1": 0, "x2": 1267, "y2": 13, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1433, "y1": 14, "x2": 1463, "y2": 29, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1402, "y1": 15, "x2": 1432, "y2": 35, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 908, "y1": 3, "x2": 926, "y2": 13, "logit": 0.0}, "18": {"instance_id": 18, "class_name": "car", "x1": 973, "y1": 0, "x2": 989, "y2": 14, "logit": 0.0}, "19": {"instance_id": 19, "class_name": "car", "x1": 947, "y1": 0, "x2": 957, "y2": 12, "logit": 0.0}}}