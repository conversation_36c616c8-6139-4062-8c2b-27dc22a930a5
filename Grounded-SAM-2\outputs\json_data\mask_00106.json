{"mask_name": "mask_00106.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"13": {"instance_id": 13, "class_name": "car", "x1": 1025, "y1": 193, "x2": 1098, "y2": 264, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 904, "y1": 136, "x2": 964, "y2": 200, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1330, "y1": 78, "x2": 1393, "y2": 120, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1115, "y1": 136, "x2": 1178, "y2": 193, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1569, "y1": 14, "x2": 1617, "y2": 36, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 910, "y1": 78, "x2": 950, "y2": 118, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 857, "y1": 36, "x2": 888, "y2": 64, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 971, "y1": 49, "x2": 1008, "y2": 78, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1316, "y1": 10, "x2": 1351, "y2": 31, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1122, "y1": 8, "x2": 1145, "y2": 28, "logit": 0.0}, "35": {"instance_id": 35, "class_name": "car", "x1": 878, "y1": 0, "x2": 899, "y2": 20, "logit": 0.0}, "36": {"instance_id": 36, "class_name": "car", "x1": 1613, "y1": 21, "x2": 1659, "y2": 47, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1810, "y1": 43, "x2": 1879, "y2": 78, "logit": 0.0}, "38": {"instance_id": 38, "class_name": "car", "x1": 1710, "y1": 7, "x2": 1723, "y2": 23, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1101, "y1": 0, "x2": 1129, "y2": 15, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "40": {"instance_id": 40, "class_name": "car", "x1": 935, "y1": 0, "x2": 953, "y2": 13, "logit": 0.0}}}