{"mask_name": "mask_00155.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"30": {"instance_id": 30, "class_name": "car", "x1": 486, "y1": 400, "x2": 670, "y2": 589, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1068, "y1": 670, "x2": 1352, "y2": 1079, "logit": 0.0}, "45": {"instance_id": 45, "class_name": "car", "x1": 1569, "y1": 10, "x2": 1613, "y2": 34, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 842, "y1": 66, "x2": 880, "y2": 101, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1466, "y1": 125, "x2": 1563, "y2": 191, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 981, "y1": 33, "x2": 1013, "y2": 59, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1549, "y1": 102, "x2": 1646, "y2": 171, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 988, "y1": 56, "x2": 1022, "y2": 90, "logit": 0.0}, "46": {"instance_id": 46, "class_name": "car", "x1": 867, "y1": 8, "x2": 898, "y2": 43, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1327, "y1": 0, "x2": 1356, "y2": 8, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1440, "y1": 0, "x2": 1476, "y2": 16, "logit": 0.0}, "47": {"instance_id": 47, "class_name": "car", "x1": 1121, "y1": 55, "x2": 1160, "y2": 76, "logit": 0.0}}}