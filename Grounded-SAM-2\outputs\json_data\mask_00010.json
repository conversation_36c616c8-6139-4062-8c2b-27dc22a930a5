{"mask_name": "mask_00010.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "car", "x1": 1134, "y1": 25, "x2": 1161, "y2": 44, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1119, "y1": 148, "x2": 1187, "y2": 203, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1026, "y1": 153, "x2": 1085, "y2": 213, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1662, "y1": 48, "x2": 1709, "y2": 72, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 978, "y1": 47, "x2": 1005, "y2": 70, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1004, "y1": 80, "x2": 1047, "y2": 114, "logit": 0.0}, "7": {"instance_id": 7, "class_name": "car", "x1": 1241, "y1": 5, "x2": 1264, "y2": 20, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1589, "y1": 44, "x2": 1630, "y2": 72, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1325, "y1": 8, "x2": 1358, "y2": 30, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1036, "y1": 43, "x2": 1064, "y2": 66, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 920, "y1": 8, "x2": 935, "y2": 20, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1586, "y1": 44, "x2": 1599, "y2": 61, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 962, "y1": 5, "x2": 977, "y2": 16, "logit": 0.0}, "14": {"instance_id": 14, "class_name": "car", "x1": 1586, "y1": 43, "x2": 1602, "y2": 62, "logit": 0.0}, "15": {"instance_id": 15, "class_name": "car", "x1": 1172, "y1": 0, "x2": 1178, "y2": 14, "logit": 0.0}, "16": {"instance_id": 16, "class_name": "car", "x1": 1000, "y1": 0, "x2": 1018, "y2": 7, "logit": 0.0}}}