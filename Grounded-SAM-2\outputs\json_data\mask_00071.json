{"mask_name": "mask_00071.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"20": {"instance_id": 20, "class_name": "car", "x1": 1670, "y1": 207, "x2": 1810, "y2": 305, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1583, "y1": 26, "x2": 1625, "y2": 52, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1035, "y1": 874, "x2": 1312, "y2": 1079, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1141, "y1": 14, "x2": 1163, "y2": 37, "logit": 0.0}, "27": {"instance_id": 27, "class_name": "car", "x1": 1533, "y1": 15, "x2": 1581, "y2": 41, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1008, "y1": 44, "x2": 1037, "y2": 70, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 940, "y1": 14, "x2": 960, "y2": 32, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 953, "y1": 18, "x2": 975, "y2": 43, "logit": 0.0}, "28": {"instance_id": 28, "class_name": "car", "x1": 891, "y1": 3, "x2": 909, "y2": 22, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 923, "y1": 40, "x2": 954, "y2": 69, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1028, "y1": 27, "x2": 1054, "y2": 50, "logit": 0.0}}}