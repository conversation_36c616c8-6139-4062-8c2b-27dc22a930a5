{"mask_name": "mask_00054.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"10": {"instance_id": 10, "class_name": "car", "x1": 1188, "y1": 205, "x2": 1277, "y2": 289, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1011, "y1": 204, "x2": 1091, "y2": 283, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1113, "y1": 882, "x2": 1408, "y2": 1079, "logit": 0.0}, "20": {"instance_id": 20, "class_name": "car", "x1": 1358, "y1": 90, "x2": 1422, "y2": 136, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 928, "y1": 20, "x2": 954, "y2": 45, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1247, "y1": 0, "x2": 1267, "y2": 9, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1295, "y1": 0, "x2": 1314, "y2": 11, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 955, "y1": 8, "x2": 975, "y2": 27, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1002, "y1": 16, "x2": 1025, "y2": 43, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1313, "y1": 0, "x2": 1341, "y2": 12, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1001, "y1": 14, "x2": 1020, "y2": 28, "logit": 0.0}, "23": {"instance_id": 23, "class_name": "car", "x1": 1143, "y1": 65, "x2": 1177, "y2": 94, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 925, "y1": 5, "x2": 945, "y2": 22, "logit": 0.0}, "24": {"instance_id": 24, "class_name": "car", "x1": 895, "y1": 0, "x2": 911, "y2": 12, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1094, "y1": 4, "x2": 1119, "y2": 22, "logit": 0.0}}}