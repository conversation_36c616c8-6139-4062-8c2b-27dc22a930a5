{"mask_name": "mask_00153.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"30": {"instance_id": 30, "class_name": "car", "x1": 545, "y1": 348, "x2": 705, "y2": 510, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1063, "y1": 545, "x2": 1288, "y2": 835, "logit": 0.0}, "45": {"instance_id": 45, "class_name": "car", "x1": 1586, "y1": 13, "x2": 1632, "y2": 36, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 846, "y1": 64, "x2": 881, "y2": 96, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1436, "y1": 114, "x2": 1527, "y2": 175, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 978, "y1": 32, "x2": 1010, "y2": 56, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1508, "y1": 91, "x2": 1595, "y2": 154, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 986, "y1": 50, "x2": 1021, "y2": 84, "logit": 0.0}, "46": {"instance_id": 46, "class_name": "car", "x1": 869, "y1": 7, "x2": 899, "y2": 42, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1338, "y1": 0, "x2": 1366, "y2": 10, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1455, "y1": 0, "x2": 1492, "y2": 19, "logit": 0.0}, "47": {"instance_id": 47, "class_name": "car", "x1": 1121, "y1": 56, "x2": 1160, "y2": 76, "logit": 0.0}}}