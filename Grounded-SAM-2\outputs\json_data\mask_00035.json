{"mask_name": "mask_00035.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"6": {"instance_id": 6, "class_name": "car", "x1": 1040, "y1": 200, "x2": 1122, "y2": 275, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1109, "y1": 992, "x2": 1354, "y2": 1079, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1564, "y1": 921, "x2": 1892, "y2": 1079, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1001, "y1": 93, "x2": 1045, "y2": 132, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1097, "y1": 87, "x2": 1143, "y2": 129, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1440, "y1": 12, "x2": 1471, "y2": 30, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 930, "y1": 12, "x2": 950, "y2": 29, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 992, "y1": 9, "x2": 1010, "y2": 26, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1219, "y1": 0, "x2": 1241, "y2": 7, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1396, "y1": 7, "x2": 1425, "y2": 24, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1360, "y1": 10, "x2": 1389, "y2": 28, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 910, "y1": 0, "x2": 929, "y2": 14, "logit": 0.0}, "18": {"instance_id": 18, "class_name": "car", "x1": 978, "y1": 2, "x2": 994, "y2": 16, "logit": 0.0}, "19": {"instance_id": 19, "class_name": "car", "x1": 949, "y1": 0, "x2": 963, "y2": 15, "logit": 0.0}}}