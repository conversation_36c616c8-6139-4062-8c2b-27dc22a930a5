{"mask_name": "mask_00144.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"30": {"instance_id": 30, "class_name": "car", "x1": 715, "y1": 194, "x2": 800, "y2": 275, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1036, "y1": 263, "x2": 1147, "y2": 379, "logit": 0.0}, "45": {"instance_id": 45, "class_name": "car", "x1": 1692, "y1": 24, "x2": 1745, "y2": 52, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 278, "y1": 769, "x2": 641, "y2": 1079, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 860, "y1": 47, "x2": 890, "y2": 72, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1329, "y1": 73, "x2": 1395, "y2": 119, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 965, "y1": 17, "x2": 992, "y2": 41, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1360, "y1": 51, "x2": 1422, "y2": 97, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 975, "y1": 33, "x2": 1004, "y2": 61, "logit": 0.0}, "46": {"instance_id": 46, "class_name": "car", "x1": 873, "y1": 0, "x2": 900, "y2": 31, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1395, "y1": 0, "x2": 1429, "y2": 18, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1534, "y1": 10, "x2": 1578, "y2": 31, "logit": 0.0}, "47": {"instance_id": 47, "class_name": "car", "x1": 1120, "y1": 56, "x2": 1160, "y2": 77, "logit": 0.0}}}