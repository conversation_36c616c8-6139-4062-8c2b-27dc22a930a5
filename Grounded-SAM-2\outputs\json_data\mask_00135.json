{"mask_name": "mask_00135.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"31": {"instance_id": 31, "class_name": "car", "x1": 726, "y1": 322, "x2": 856, "y2": 460, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1016, "y1": 160, "x2": 1089, "y2": 227, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 782, "y1": 126, "x2": 844, "y2": 181, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1634, "y1": 21, "x2": 1681, "y2": 44, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1321, "y1": 0, "x2": 1352, "y2": 9, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1462, "y1": 4, "x2": 1501, "y2": 29, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 862, "y1": 31, "x2": 893, "y2": 55, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1267, "y1": 26, "x2": 1313, "y2": 61, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1255, "y1": 48, "x2": 1307, "y2": 83, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 963, "y1": 20, "x2": 988, "y2": 44, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 954, "y1": 10, "x2": 975, "y2": 28, "logit": 0.0}}}