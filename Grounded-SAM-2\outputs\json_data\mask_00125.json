{"mask_name": "mask_00125.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"31": {"instance_id": 31, "class_name": "car", "x1": 847, "y1": 177, "x2": 918, "y2": 246, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1705, "y1": 224, "x2": 1862, "y2": 326, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1473, "y1": 704, "x2": 1806, "y2": 1079, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 867, "y1": 399, "x2": 1032, "y2": 602, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 997, "y1": 97, "x2": 1054, "y2": 146, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 818, "y1": 81, "x2": 868, "y2": 123, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1775, "y1": 35, "x2": 1839, "y2": 64, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1400, "y1": 0, "x2": 1437, "y2": 19, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1555, "y1": 14, "x2": 1600, "y2": 41, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 874, "y1": 15, "x2": 897, "y2": 40, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1192, "y1": 11, "x2": 1227, "y2": 39, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1197, "y1": 30, "x2": 1239, "y2": 57, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 952, "y1": 10, "x2": 975, "y2": 30, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 941, "y1": 0, "x2": 961, "y2": 18, "logit": 0.0}}}