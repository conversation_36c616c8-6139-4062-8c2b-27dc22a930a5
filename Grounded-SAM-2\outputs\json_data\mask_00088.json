{"mask_name": "mask_00088.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"22": {"instance_id": 22, "class_name": "car", "x1": 1054, "y1": 57, "x2": 1090, "y2": 89, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1011, "y1": 81, "x2": 1053, "y2": 121, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1501, "y1": 38, "x2": 1553, "y2": 70, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1381, "y1": 0, "x2": 1434, "y2": 21, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1208, "y1": 34, "x2": 1247, "y2": 61, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 911, "y1": 68, "x2": 950, "y2": 109, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 876, "y1": 8, "x2": 900, "y2": 37, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 936, "y1": 38, "x2": 964, "y2": 68, "logit": 0.0}, "28": {"instance_id": 28, "class_name": "car", "x1": 876, "y1": 15, "x2": 899, "y2": 37, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 955, "y1": 25, "x2": 981, "y2": 46, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1077, "y1": 0, "x2": 1105, "y2": 14, "logit": 0.0}, "33": {"instance_id": 33, "class_name": "car", "x1": 876, "y1": 16, "x2": 899, "y2": 36, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 936, "y1": 40, "x2": 964, "y2": 68, "logit": 0.0}}}