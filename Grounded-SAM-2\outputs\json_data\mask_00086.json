{"mask_name": "mask_00086.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"22": {"instance_id": 22, "class_name": "car", "x1": 1050, "y1": 52, "x2": 1087, "y2": 84, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1011, "y1": 79, "x2": 1052, "y2": 114, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1526, "y1": 40, "x2": 1582, "y2": 77, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1399, "y1": 0, "x2": 1446, "y2": 24, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1200, "y1": 31, "x2": 1239, "y2": 58, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 912, "y1": 64, "x2": 951, "y2": 104, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 878, "y1": 5, "x2": 901, "y2": 35, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 938, "y1": 26, "x2": 979, "y2": 65, "logit": 0.0}, "28": {"instance_id": 28, "class_name": "car", "x1": 877, "y1": 15, "x2": 900, "y2": 35, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 954, "y1": 25, "x2": 980, "y2": 44, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1075, "y1": 0, "x2": 1102, "y2": 14, "logit": 0.0}, "33": {"instance_id": 33, "class_name": "car", "x1": 877, "y1": 15, "x2": 900, "y2": 35, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 938, "y1": 37, "x2": 967, "y2": 65, "logit": 0.0}}}