{"mask_name": "mask_00058.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"10": {"instance_id": 10, "class_name": "car", "x1": 1231, "y1": 266, "x2": 1345, "y2": 374, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1016, "y1": 258, "x2": 1116, "y2": 362, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "20": {"instance_id": 20, "class_name": "car", "x1": 1408, "y1": 106, "x2": 1481, "y2": 160, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 926, "y1": 23, "x2": 955, "y2": 50, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1225, "y1": 0, "x2": 1247, "y2": 6, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 955, "y1": 12, "x2": 976, "y2": 29, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1004, "y1": 22, "x2": 1030, "y2": 48, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1303, "y1": 0, "x2": 1314, "y2": 6, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1008, "y1": 16, "x2": 1030, "y2": 29, "logit": 0.0}, "23": {"instance_id": 23, "class_name": "car", "x1": 1143, "y1": 65, "x2": 1177, "y2": 93, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 928, "y1": 9, "x2": 951, "y2": 25, "logit": 0.0}, "24": {"instance_id": 24, "class_name": "car", "x1": 896, "y1": 0, "x2": 912, "y2": 14, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1106, "y1": 6, "x2": 1132, "y2": 25, "logit": 0.0}}}