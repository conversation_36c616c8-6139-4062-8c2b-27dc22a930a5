{"mask_name": "mask_00047.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"10": {"instance_id": 10, "class_name": "car", "x1": 1144, "y1": 146, "x2": 1211, "y2": 208, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1007, "y1": 148, "x2": 1070, "y2": 207, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1065, "y1": 432, "x2": 1237, "y2": 622, "logit": 0.0}, "20": {"instance_id": 20, "class_name": "car", "x1": 1300, "y1": 70, "x2": 1353, "y2": 109, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 929, "y1": 15, "x2": 953, "y2": 38, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1283, "y1": 0, "x2": 1308, "y2": 15, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1326, "y1": 0, "x2": 1354, "y2": 12, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 953, "y1": 6, "x2": 971, "y2": 21, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 998, "y1": 15, "x2": 1021, "y2": 35, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1355, "y1": 0, "x2": 1385, "y2": 18, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 992, "y1": 8, "x2": 1011, "y2": 23, "logit": 0.0}, "23": {"instance_id": 23, "class_name": "car", "x1": 1145, "y1": 66, "x2": 1179, "y2": 94, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 920, "y1": 3, "x2": 941, "y2": 18, "logit": 0.0}, "24": {"instance_id": 24, "class_name": "car", "x1": 896, "y1": 0, "x2": 911, "y2": 10, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1079, "y1": 0, "x2": 1103, "y2": 18, "logit": 0.0}}}