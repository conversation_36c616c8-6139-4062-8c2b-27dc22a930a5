{"mask_name": "mask_00101.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"13": {"instance_id": 13, "class_name": "car", "x1": 1019, "y1": 148, "x2": 1080, "y2": 209, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 907, "y1": 112, "x2": 958, "y2": 165, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1289, "y1": 63, "x2": 1342, "y2": 99, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1092, "y1": 105, "x2": 1145, "y2": 152, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1623, "y1": 20, "x2": 1679, "y2": 45, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 916, "y1": 66, "x2": 953, "y2": 99, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 864, "y1": 31, "x2": 893, "y2": 55, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 966, "y1": 41, "x2": 1000, "y2": 66, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1358, "y1": 16, "x2": 1395, "y2": 39, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1111, "y1": 4, "x2": 1140, "y2": 24, "logit": 0.0}, "35": {"instance_id": 35, "class_name": "car", "x1": 884, "y1": 0, "x2": 900, "y2": 16, "logit": 0.0}, "36": {"instance_id": 36, "class_name": "car", "x1": 1689, "y1": 29, "x2": 1739, "y2": 58, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1902, "y1": 63, "x2": 1919, "y2": 88, "logit": 0.0}, "38": {"instance_id": 38, "class_name": "car", "x1": 1714, "y1": 8, "x2": 1728, "y2": 26, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1086, "y1": 0, "x2": 1110, "y2": 11, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1320, "y1": 0, "x2": 1348, "y2": 8, "logit": 0.0}, "40": {"instance_id": 40, "class_name": "car", "x1": 932, "y1": 0, "x2": 950, "y2": 8, "logit": 0.0}}}