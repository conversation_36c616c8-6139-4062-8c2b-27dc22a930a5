{"mask_name": "mask_00055.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"10": {"instance_id": 10, "class_name": "car", "x1": 1198, "y1": 220, "x2": 1293, "y2": 308, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1012, "y1": 217, "x2": 1097, "y2": 305, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "20": {"instance_id": 20, "class_name": "car", "x1": 1372, "y1": 94, "x2": 1437, "y2": 143, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 927, "y1": 21, "x2": 954, "y2": 47, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1240, "y1": 0, "x2": 1262, "y2": 9, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 954, "y1": 9, "x2": 976, "y2": 27, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1003, "y1": 16, "x2": 1026, "y2": 44, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1309, "y1": 0, "x2": 1334, "y2": 11, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1004, "y1": 14, "x2": 1022, "y2": 28, "logit": 0.0}, "23": {"instance_id": 23, "class_name": "car", "x1": 1143, "y1": 65, "x2": 1176, "y2": 93, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 926, "y1": 6, "x2": 946, "y2": 23, "logit": 0.0}, "24": {"instance_id": 24, "class_name": "car", "x1": 895, "y1": 0, "x2": 910, "y2": 12, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1097, "y1": 4, "x2": 1123, "y2": 23, "logit": 0.0}}}