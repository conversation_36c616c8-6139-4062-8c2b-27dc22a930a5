{"mask_name": "mask_00150.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"30": {"instance_id": 30, "class_name": "car", "x1": 624, "y1": 280, "x2": 749, "y2": 403, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1051, "y1": 409, "x2": 1223, "y2": 609, "logit": 0.0}, "45": {"instance_id": 45, "class_name": "car", "x1": 1619, "y1": 15, "x2": 1667, "y2": 40, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 851, "y1": 55, "x2": 885, "y2": 87, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1395, "y1": 97, "x2": 1475, "y2": 153, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 973, "y1": 27, "x2": 1003, "y2": 51, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1451, "y1": 74, "x2": 1525, "y2": 130, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 983, "y1": 45, "x2": 1015, "y2": 76, "logit": 0.0}, "46": {"instance_id": 46, "class_name": "car", "x1": 871, "y1": 0, "x2": 899, "y2": 38, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1356, "y1": 0, "x2": 1386, "y2": 12, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1480, "y1": 3, "x2": 1518, "y2": 22, "logit": 0.0}, "47": {"instance_id": 47, "class_name": "car", "x1": 1121, "y1": 56, "x2": 1160, "y2": 76, "logit": 0.0}}}