{"mask_name": "mask_00154.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"30": {"instance_id": 30, "class_name": "car", "x1": 529, "y1": 364, "x2": 694, "y2": 534, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1065, "y1": 581, "x2": 1308, "y2": 903, "logit": 0.0}, "45": {"instance_id": 45, "class_name": "car", "x1": 1583, "y1": 12, "x2": 1627, "y2": 35, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 846, "y1": 64, "x2": 881, "y2": 97, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1446, "y1": 117, "x2": 1539, "y2": 179, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 980, "y1": 32, "x2": 1011, "y2": 57, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1521, "y1": 94, "x2": 1613, "y2": 159, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 987, "y1": 52, "x2": 1023, "y2": 86, "logit": 0.0}, "46": {"instance_id": 46, "class_name": "car", "x1": 869, "y1": 7, "x2": 899, "y2": 42, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1335, "y1": 0, "x2": 1363, "y2": 8, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1452, "y1": 0, "x2": 1485, "y2": 17, "logit": 0.0}, "47": {"instance_id": 47, "class_name": "car", "x1": 1121, "y1": 55, "x2": 1160, "y2": 76, "logit": 0.0}}}