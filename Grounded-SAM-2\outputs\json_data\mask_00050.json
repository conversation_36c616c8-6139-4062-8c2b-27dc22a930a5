{"mask_name": "mask_00050.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"10": {"instance_id": 10, "class_name": "car", "x1": 1162, "y1": 171, "x2": 1235, "y2": 239, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1010, "y1": 172, "x2": 1078, "y2": 238, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1084, "y1": 568, "x2": 1309, "y2": 861, "logit": 0.0}, "20": {"instance_id": 20, "class_name": "car", "x1": 1324, "y1": 80, "x2": 1382, "y2": 121, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 929, "y1": 17, "x2": 953, "y2": 42, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1265, "y1": 0, "x2": 1289, "y2": 13, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1308, "y1": 0, "x2": 1336, "y2": 11, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 954, "y1": 4, "x2": 974, "y2": 24, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1000, "y1": 18, "x2": 1023, "y2": 39, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1337, "y1": 0, "x2": 1364, "y2": 15, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 997, "y1": 9, "x2": 1015, "y2": 27, "logit": 0.0}, "23": {"instance_id": 23, "class_name": "car", "x1": 1145, "y1": 66, "x2": 1177, "y2": 93, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 922, "y1": 3, "x2": 943, "y2": 20, "logit": 0.0}, "24": {"instance_id": 24, "class_name": "car", "x1": 899, "y1": 0, "x2": 911, "y2": 11, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1086, "y1": 0, "x2": 1109, "y2": 19, "logit": 0.0}}}