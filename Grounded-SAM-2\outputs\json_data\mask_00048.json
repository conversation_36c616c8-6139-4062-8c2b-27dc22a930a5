{"mask_name": "mask_00048.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"10": {"instance_id": 10, "class_name": "car", "x1": 1147, "y1": 151, "x2": 1214, "y2": 210, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1008, "y1": 152, "x2": 1072, "y2": 212, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1067, "y1": 456, "x2": 1248, "y2": 655, "logit": 0.0}, "20": {"instance_id": 20, "class_name": "car", "x1": 1305, "y1": 71, "x2": 1358, "y2": 111, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 929, "y1": 15, "x2": 953, "y2": 39, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1279, "y1": 0, "x2": 1304, "y2": 15, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1322, "y1": 0, "x2": 1350, "y2": 12, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 952, "y1": 4, "x2": 973, "y2": 22, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 998, "y1": 15, "x2": 1021, "y2": 36, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1351, "y1": 0, "x2": 1380, "y2": 17, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 992, "y1": 8, "x2": 1012, "y2": 25, "logit": 0.0}, "23": {"instance_id": 23, "class_name": "car", "x1": 1144, "y1": 66, "x2": 1179, "y2": 94, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 920, "y1": 3, "x2": 942, "y2": 18, "logit": 0.0}, "24": {"instance_id": 24, "class_name": "car", "x1": 898, "y1": 0, "x2": 911, "y2": 9, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1079, "y1": 0, "x2": 1104, "y2": 18, "logit": 0.0}}}