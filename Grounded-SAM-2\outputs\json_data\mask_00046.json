{"mask_name": "mask_00046.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"10": {"instance_id": 10, "class_name": "car", "x1": 1138, "y1": 138, "x2": 1201, "y2": 196, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1007, "y1": 141, "x2": 1068, "y2": 197, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1060, "y1": 393, "x2": 1218, "y2": 552, "logit": 0.0}, "20": {"instance_id": 20, "class_name": "car", "x1": 1292, "y1": 68, "x2": 1343, "y2": 105, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 930, "y1": 15, "x2": 953, "y2": 37, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1291, "y1": 0, "x2": 1312, "y2": 17, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1334, "y1": 0, "x2": 1364, "y2": 15, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 953, "y1": 6, "x2": 970, "y2": 21, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 997, "y1": 15, "x2": 1020, "y2": 35, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1363, "y1": 3, "x2": 1393, "y2": 19, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 992, "y1": 7, "x2": 1009, "y2": 23, "logit": 0.0}, "23": {"instance_id": 23, "class_name": "car", "x1": 1144, "y1": 67, "x2": 1179, "y2": 94, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 919, "y1": 0, "x2": 942, "y2": 19, "logit": 0.0}, "24": {"instance_id": 24, "class_name": "car", "x1": 895, "y1": 0, "x2": 910, "y2": 10, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1076, "y1": 0, "x2": 1100, "y2": 17, "logit": 0.0}}}