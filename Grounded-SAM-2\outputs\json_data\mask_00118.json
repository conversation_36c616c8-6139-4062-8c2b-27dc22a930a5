{"mask_name": "mask_00118.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"13": {"instance_id": 13, "class_name": "car", "x1": 1051, "y1": 463, "x2": 1209, "y2": 662, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 892, "y1": 246, "x2": 991, "y2": 359, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1503, "y1": 142, "x2": 1603, "y2": 212, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1235, "y1": 304, "x2": 1364, "y2": 432, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1456, "y1": 0, "x2": 1503, "y2": 28, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 881, "y1": 125, "x2": 938, "y2": 180, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 835, "y1": 62, "x2": 877, "y2": 95, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 987, "y1": 73, "x2": 1033, "y2": 115, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1233, "y1": 0, "x2": 1262, "y2": 15, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1172, "y1": 18, "x2": 1202, "y2": 43, "logit": 0.0}, "35": {"instance_id": 35, "class_name": "car", "x1": 875, "y1": 0, "x2": 896, "y2": 31, "logit": 0.0}, "36": {"instance_id": 36, "class_name": "car", "x1": 1457, "y1": 0, "x2": 1503, "y2": 28, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1635, "y1": 22, "x2": 1686, "y2": 52, "logit": 0.0}, "38": {"instance_id": 38, "class_name": "car", "x1": 1708, "y1": 5, "x2": 1721, "y2": 23, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1169, "y1": 4, "x2": 1186, "y2": 28, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "40": {"instance_id": 40, "class_name": "car", "x1": 944, "y1": 0, "x2": 965, "y2": 22, "logit": 0.0}}}