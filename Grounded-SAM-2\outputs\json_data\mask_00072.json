{"mask_name": "mask_00072.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"20": {"instance_id": 20, "class_name": "car", "x1": 1690, "y1": 215, "x2": 1836, "y2": 315, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1562, "y1": 25, "x2": 1615, "y2": 48, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1039, "y1": 962, "x2": 1309, "y2": 1079, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1143, "y1": 14, "x2": 1170, "y2": 37, "logit": 0.0}, "27": {"instance_id": 27, "class_name": "car", "x1": 1527, "y1": 14, "x2": 1575, "y2": 39, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1009, "y1": 45, "x2": 1038, "y2": 71, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 940, "y1": 14, "x2": 960, "y2": 32, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 952, "y1": 19, "x2": 975, "y2": 43, "logit": 0.0}, "28": {"instance_id": 28, "class_name": "car", "x1": 891, "y1": 3, "x2": 909, "y2": 22, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 923, "y1": 40, "x2": 953, "y2": 70, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1029, "y1": 28, "x2": 1055, "y2": 51, "logit": 0.0}}}