{"mask_name": "mask_00127.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"31": {"instance_id": 31, "class_name": "car", "x1": 834, "y1": 193, "x2": 913, "y2": 272, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1779, "y1": 256, "x2": 1919, "y2": 369, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1628, "y1": 977, "x2": 1919, "y2": 1079, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 852, "y1": 464, "x2": 1046, "y2": 711, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1000, "y1": 107, "x2": 1057, "y2": 158, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 818, "y1": 87, "x2": 864, "y2": 130, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1745, "y1": 32, "x2": 1807, "y2": 61, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1385, "y1": 0, "x2": 1424, "y2": 17, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1536, "y1": 12, "x2": 1581, "y2": 39, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 873, "y1": 15, "x2": 897, "y2": 43, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1202, "y1": 14, "x2": 1241, "y2": 40, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1207, "y1": 32, "x2": 1249, "y2": 61, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 955, "y1": 13, "x2": 978, "y2": 32, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 943, "y1": 2, "x2": 963, "y2": 19, "logit": 0.0}}}