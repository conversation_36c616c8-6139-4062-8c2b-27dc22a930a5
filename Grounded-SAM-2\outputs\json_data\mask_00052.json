{"mask_name": "mask_00052.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"10": {"instance_id": 10, "class_name": "car", "x1": 1174, "y1": 186, "x2": 1256, "y2": 261, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1010, "y1": 186, "x2": 1085, "y2": 258, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1097, "y1": 693, "x2": 1381, "y2": 1079, "logit": 0.0}, "20": {"instance_id": 20, "class_name": "car", "x1": 1341, "y1": 84, "x2": 1401, "y2": 128, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 928, "y1": 19, "x2": 953, "y2": 43, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1256, "y1": 0, "x2": 1279, "y2": 11, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1307, "y1": 0, "x2": 1328, "y2": 10, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 954, "y1": 5, "x2": 974, "y2": 25, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1001, "y1": 17, "x2": 1024, "y2": 41, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1325, "y1": 0, "x2": 1353, "y2": 14, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1000, "y1": 11, "x2": 1017, "y2": 27, "logit": 0.0}, "23": {"instance_id": 23, "class_name": "car", "x1": 1144, "y1": 65, "x2": 1177, "y2": 93, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 924, "y1": 3, "x2": 943, "y2": 22, "logit": 0.0}, "24": {"instance_id": 24, "class_name": "car", "x1": 896, "y1": 0, "x2": 911, "y2": 11, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1090, "y1": 3, "x2": 1115, "y2": 21, "logit": 0.0}}}