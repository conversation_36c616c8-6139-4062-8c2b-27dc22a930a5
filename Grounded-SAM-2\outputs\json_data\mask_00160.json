{"mask_name": "mask_00160.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"30": {"instance_id": 30, "class_name": "car", "x1": 177, "y1": 644, "x2": 508, "y2": 1018, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "45": {"instance_id": 45, "class_name": "car", "x1": 1523, "y1": 5, "x2": 1566, "y2": 34, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 834, "y1": 81, "x2": 873, "y2": 119, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1561, "y1": 163, "x2": 1683, "y2": 241, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 985, "y1": 44, "x2": 1023, "y2": 72, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1693, "y1": 144, "x2": 1826, "y2": 232, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 995, "y1": 70, "x2": 1034, "y2": 106, "logit": 0.0}, "46": {"instance_id": 46, "class_name": "car", "x1": 865, "y1": 10, "x2": 902, "y2": 48, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1407, "y1": 0, "x2": 1438, "y2": 11, "logit": 0.0}, "47": {"instance_id": 47, "class_name": "car", "x1": 1121, "y1": 55, "x2": 1161, "y2": 75, "logit": 0.0}}}