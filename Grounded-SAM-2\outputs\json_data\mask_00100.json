{"mask_name": "mask_00100.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"22": {"instance_id": 22, "class_name": "car", "x1": 1087, "y1": 96, "x2": 1139, "y2": 145, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1018, "y1": 142, "x2": 1076, "y2": 197, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1367, "y1": 16, "x2": 1407, "y2": 41, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1322, "y1": 0, "x2": 1356, "y2": 10, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1280, "y1": 58, "x2": 1332, "y2": 95, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 908, "y1": 107, "x2": 956, "y2": 160, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 864, "y1": 30, "x2": 893, "y2": 53, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 919, "y1": 63, "x2": 954, "y2": 97, "logit": 0.0}, "28": {"instance_id": 28, "class_name": "car", "x1": 864, "y1": 30, "x2": 893, "y2": 53, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 964, "y1": 39, "x2": 998, "y2": 65, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1106, "y1": 3, "x2": 1137, "y2": 23, "logit": 0.0}, "33": {"instance_id": 33, "class_name": "car", "x1": 864, "y1": 30, "x2": 893, "y2": 53, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 920, "y1": 63, "x2": 955, "y2": 97, "logit": 0.0}}}