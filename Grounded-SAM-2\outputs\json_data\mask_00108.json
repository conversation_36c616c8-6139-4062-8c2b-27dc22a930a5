{"mask_name": "mask_00108.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"13": {"instance_id": 13, "class_name": "car", "x1": 1029, "y1": 222, "x2": 1110, "y2": 308, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 902, "y1": 153, "x2": 969, "y2": 221, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1354, "y1": 85, "x2": 1423, "y2": 134, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1131, "y1": 156, "x2": 1199, "y2": 220, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1542, "y1": 12, "x2": 1585, "y2": 39, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 906, "y1": 84, "x2": 948, "y2": 127, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 853, "y1": 43, "x2": 885, "y2": 69, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 973, "y1": 53, "x2": 1012, "y2": 84, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1296, "y1": 8, "x2": 1330, "y2": 29, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1131, "y1": 9, "x2": 1153, "y2": 31, "logit": 0.0}, "35": {"instance_id": 35, "class_name": "car", "x1": 877, "y1": 0, "x2": 899, "y2": 23, "logit": 0.0}, "36": {"instance_id": 36, "class_name": "car", "x1": 1581, "y1": 17, "x2": 1623, "y2": 44, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1769, "y1": 39, "x2": 1836, "y2": 73, "logit": 0.0}, "38": {"instance_id": 38, "class_name": "car", "x1": 1710, "y1": 7, "x2": 1721, "y2": 24, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1110, "y1": 0, "x2": 1136, "y2": 18, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "40": {"instance_id": 40, "class_name": "car", "x1": 935, "y1": 0, "x2": 955, "y2": 15, "logit": 0.0}}}