{"mask_name": "mask_019.npy", "mask_height": 240, "mask_width": 360, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "bike", "x1": 100, "y1": 128, "x2": 135, "y2": 146, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "bike", "x1": 100, "y1": 129, "x2": 135, "y2": 146, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "bike", "x1": 122, "y1": 134, "x2": 135, "y2": 146, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "bike", "x1": 101, "y1": 133, "x2": 135, "y2": 146, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "bike", "x1": 204, "y1": 222, "x2": 208, "y2": 232, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "bike", "x1": 195, "y1": 225, "x2": 205, "y2": 239, "logit": 0.0}}}