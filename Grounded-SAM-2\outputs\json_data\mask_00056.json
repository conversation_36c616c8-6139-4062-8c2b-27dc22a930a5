{"mask_name": "mask_00056.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"10": {"instance_id": 10, "class_name": "car", "x1": 1211, "y1": 237, "x2": 1312, "y2": 335, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1014, "y1": 234, "x2": 1104, "y2": 324, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "20": {"instance_id": 20, "class_name": "car", "x1": 1385, "y1": 99, "x2": 1455, "y2": 149, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 928, "y1": 22, "x2": 955, "y2": 48, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1234, "y1": 0, "x2": 1255, "y2": 8, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 954, "y1": 11, "x2": 976, "y2": 28, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1004, "y1": 17, "x2": 1028, "y2": 45, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1306, "y1": 0, "x2": 1327, "y2": 9, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1006, "y1": 15, "x2": 1024, "y2": 30, "logit": 0.0}, "23": {"instance_id": 23, "class_name": "car", "x1": 1143, "y1": 65, "x2": 1177, "y2": 94, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 926, "y1": 8, "x2": 949, "y2": 24, "logit": 0.0}, "24": {"instance_id": 24, "class_name": "car", "x1": 895, "y1": 0, "x2": 911, "y2": 13, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1102, "y1": 5, "x2": 1126, "y2": 24, "logit": 0.0}}}