{"mask_name": "mask_00158.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"30": {"instance_id": 30, "class_name": "car", "x1": 331, "y1": 528, "x2": 584, "y2": 802, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1135, "y1": 1039, "x2": 1365, "y2": 1079, "logit": 0.0}, "45": {"instance_id": 45, "class_name": "car", "x1": 1538, "y1": 8, "x2": 1582, "y2": 35, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 837, "y1": 76, "x2": 876, "y2": 111, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1521, "y1": 146, "x2": 1633, "y2": 221, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 986, "y1": 37, "x2": 1021, "y2": 67, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1631, "y1": 125, "x2": 1747, "y2": 206, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 992, "y1": 62, "x2": 1030, "y2": 99, "logit": 0.0}, "46": {"instance_id": 46, "class_name": "car", "x1": 865, "y1": 10, "x2": 898, "y2": 46, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1312, "y1": 0, "x2": 1339, "y2": 5, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1419, "y1": 0, "x2": 1453, "y2": 11, "logit": 0.0}, "47": {"instance_id": 47, "class_name": "car", "x1": 1121, "y1": 54, "x2": 1161, "y2": 75, "logit": 0.0}}}