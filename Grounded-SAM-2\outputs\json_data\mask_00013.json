{"mask_name": "mask_00013.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "car", "x1": 1143, "y1": 27, "x2": 1168, "y2": 46, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1138, "y1": 169, "x2": 1213, "y2": 229, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1032, "y1": 176, "x2": 1096, "y2": 241, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1631, "y1": 43, "x2": 1675, "y2": 65, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 982, "y1": 49, "x2": 1012, "y2": 75, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1010, "y1": 82, "x2": 1054, "y2": 123, "logit": 0.0}, "7": {"instance_id": 7, "class_name": "car", "x1": 1231, "y1": 2, "x2": 1254, "y2": 16, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1554, "y1": 39, "x2": 1598, "y2": 63, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1311, "y1": 5, "x2": 1344, "y2": 27, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1043, "y1": 48, "x2": 1072, "y2": 69, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 923, "y1": 6, "x2": 939, "y2": 20, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 965, "y1": 2, "x2": 981, "y2": 17, "logit": 0.0}, "14": {"instance_id": 14, "class_name": "car", "x1": 1555, "y1": 37, "x2": 1596, "y2": 61, "logit": 0.0}, "15": {"instance_id": 15, "class_name": "car", "x1": 1171, "y1": 0, "x2": 1179, "y2": 15, "logit": 0.0}, "16": {"instance_id": 16, "class_name": "car", "x1": 1007, "y1": 0, "x2": 1023, "y2": 7, "logit": 0.0}}}