{"mask_name": "mask_00122.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"31": {"instance_id": 31, "class_name": "car", "x1": 865, "y1": 153, "x2": 928, "y2": 215, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1605, "y1": 183, "x2": 1732, "y2": 267, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1336, "y1": 466, "x2": 1537, "y2": 689, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1077, "y1": 766, "x2": 1335, "y2": 1079, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 879, "y1": 319, "x2": 1015, "y2": 472, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 991, "y1": 85, "x2": 1044, "y2": 129, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 830, "y1": 70, "x2": 873, "y2": 110, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1827, "y1": 41, "x2": 1894, "y2": 73, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1422, "y1": 0, "x2": 1462, "y2": 23, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1587, "y1": 17, "x2": 1635, "y2": 47, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 875, "y1": 13, "x2": 897, "y2": 35, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1174, "y1": 8, "x2": 1209, "y2": 33, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1181, "y1": 25, "x2": 1221, "y2": 51, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 950, "y1": 8, "x2": 971, "y2": 27, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 937, "y1": 0, "x2": 957, "y2": 15, "logit": 0.0}}}