{"mask_name": "mask_00082.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"22": {"instance_id": 22, "class_name": "car", "x1": 1045, "y1": 44, "x2": 1077, "y2": 71, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1011, "y1": 65, "x2": 1046, "y2": 98, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1591, "y1": 55, "x2": 1654, "y2": 91, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1441, "y1": 4, "x2": 1481, "y2": 31, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1185, "y1": 25, "x2": 1219, "y2": 50, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 917, "y1": 56, "x2": 952, "y2": 91, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 882, "y1": 2, "x2": 904, "y2": 31, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 944, "y1": 20, "x2": 976, "y2": 56, "logit": 0.0}, "28": {"instance_id": 28, "class_name": "car", "x1": 882, "y1": 11, "x2": 905, "y2": 31, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 950, "y1": 20, "x2": 974, "y2": 35, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1069, "y1": 0, "x2": 1093, "y2": 10, "logit": 0.0}, "33": {"instance_id": 33, "class_name": "car", "x1": 882, "y1": 13, "x2": 904, "y2": 31, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 944, "y1": 30, "x2": 973, "y2": 57, "logit": 0.0}}}