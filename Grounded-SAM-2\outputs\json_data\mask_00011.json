{"mask_name": "mask_00011.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "car", "x1": 1138, "y1": 24, "x2": 1165, "y2": 45, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1129, "y1": 155, "x2": 1197, "y2": 212, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1029, "y1": 161, "x2": 1090, "y2": 224, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1651, "y1": 46, "x2": 1696, "y2": 68, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 980, "y1": 48, "x2": 1008, "y2": 72, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1008, "y1": 81, "x2": 1050, "y2": 116, "logit": 0.0}, "7": {"instance_id": 7, "class_name": "car", "x1": 1237, "y1": 4, "x2": 1260, "y2": 18, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1575, "y1": 42, "x2": 1617, "y2": 67, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1320, "y1": 6, "x2": 1352, "y2": 28, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1038, "y1": 45, "x2": 1067, "y2": 68, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 921, "y1": 7, "x2": 937, "y2": 20, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1570, "y1": 47, "x2": 1587, "y2": 65, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 963, "y1": 4, "x2": 978, "y2": 16, "logit": 0.0}, "14": {"instance_id": 14, "class_name": "car", "x1": 1570, "y1": 43, "x2": 1594, "y2": 65, "logit": 0.0}, "15": {"instance_id": 15, "class_name": "car", "x1": 1172, "y1": 0, "x2": 1178, "y2": 15, "logit": 0.0}, "16": {"instance_id": 16, "class_name": "car", "x1": 1003, "y1": 0, "x2": 1020, "y2": 7, "logit": 0.0}}}