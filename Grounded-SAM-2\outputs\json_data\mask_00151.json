{"mask_name": "mask_00151.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"30": {"instance_id": 30, "class_name": "car", "x1": 609, "y1": 290, "x2": 741, "y2": 420, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1053, "y1": 432, "x2": 1233, "y2": 643, "logit": 0.0}, "45": {"instance_id": 45, "class_name": "car", "x1": 1611, "y1": 15, "x2": 1660, "y2": 39, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 848, "y1": 59, "x2": 884, "y2": 90, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1403, "y1": 100, "x2": 1487, "y2": 157, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 974, "y1": 29, "x2": 1006, "y2": 52, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1462, "y1": 77, "x2": 1539, "y2": 134, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 984, "y1": 47, "x2": 1015, "y2": 78, "logit": 0.0}, "46": {"instance_id": 46, "class_name": "car", "x1": 871, "y1": 0, "x2": 899, "y2": 38, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1352, "y1": 0, "x2": 1383, "y2": 11, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1474, "y1": 3, "x2": 1512, "y2": 21, "logit": 0.0}, "47": {"instance_id": 47, "class_name": "car", "x1": 1121, "y1": 56, "x2": 1160, "y2": 77, "logit": 0.0}}}