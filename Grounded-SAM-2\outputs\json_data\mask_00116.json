{"mask_name": "mask_00116.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"13": {"instance_id": 13, "class_name": "car", "x1": 1046, "y1": 391, "x2": 1179, "y2": 554, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 896, "y1": 223, "x2": 986, "y2": 320, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1467, "y1": 128, "x2": 1559, "y2": 193, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1207, "y1": 262, "x2": 1319, "y2": 372, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1471, "y1": 3, "x2": 1522, "y2": 31, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 889, "y1": 116, "x2": 940, "y2": 167, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 839, "y1": 56, "x2": 878, "y2": 89, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 983, "y1": 68, "x2": 1028, "y2": 108, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1244, "y1": 0, "x2": 1274, "y2": 17, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1171, "y1": 16, "x2": 1195, "y2": 42, "logit": 0.0}, "35": {"instance_id": 35, "class_name": "car", "x1": 876, "y1": 0, "x2": 896, "y2": 29, "logit": 0.0}, "36": {"instance_id": 36, "class_name": "car", "x1": 1482, "y1": 5, "x2": 1522, "y2": 30, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1657, "y1": 25, "x2": 1710, "y2": 56, "logit": 0.0}, "38": {"instance_id": 38, "class_name": "car", "x1": 1708, "y1": 7, "x2": 1721, "y2": 23, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "40": {"instance_id": 40, "class_name": "car", "x1": 941, "y1": 0, "x2": 964, "y2": 20, "logit": 0.0}}}