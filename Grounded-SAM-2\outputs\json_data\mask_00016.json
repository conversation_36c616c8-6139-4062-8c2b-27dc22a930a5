{"mask_name": "mask_00016.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "car", "x1": 1152, "y1": 30, "x2": 1170, "y2": 50, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1158, "y1": 192, "x2": 1241, "y2": 264, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1038, "y1": 202, "x2": 1110, "y2": 275, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1606, "y1": 37, "x2": 1644, "y2": 59, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 986, "y1": 51, "x2": 1017, "y2": 80, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1016, "y1": 95, "x2": 1062, "y2": 134, "logit": 0.0}, "7": {"instance_id": 7, "class_name": "car", "x1": 1222, "y1": 0, "x2": 1244, "y2": 14, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1525, "y1": 32, "x2": 1568, "y2": 57, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1301, "y1": 0, "x2": 1332, "y2": 24, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1051, "y1": 50, "x2": 1081, "y2": 76, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 926, "y1": 6, "x2": 943, "y2": 21, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1553, "y1": 29, "x2": 1577, "y2": 47, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 969, "y1": 0, "x2": 986, "y2": 17, "logit": 0.0}, "14": {"instance_id": 14, "class_name": "car", "x1": 1551, "y1": 28, "x2": 1577, "y2": 48, "logit": 0.0}, "15": {"instance_id": 15, "class_name": "car", "x1": 1172, "y1": 0, "x2": 1181, "y2": 14, "logit": 0.0}, "16": {"instance_id": 16, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}}}