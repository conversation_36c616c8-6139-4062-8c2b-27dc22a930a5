{"mask_name": "mask_00143.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"30": {"instance_id": 30, "class_name": "car", "x1": 730, "y1": 183, "x2": 810, "y2": 255, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1034, "y1": 242, "x2": 1137, "y2": 345, "logit": 0.0}, "45": {"instance_id": 45, "class_name": "car", "x1": 1710, "y1": 26, "x2": 1765, "y2": 54, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 406, "y1": 658, "x2": 694, "y2": 1000, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 861, "y1": 44, "x2": 890, "y2": 69, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1317, "y1": 69, "x2": 1382, "y2": 113, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 963, "y1": 17, "x2": 992, "y2": 39, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1347, "y1": 47, "x2": 1406, "y2": 91, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 974, "y1": 31, "x2": 1002, "y2": 58, "logit": 0.0}, "46": {"instance_id": 46, "class_name": "car", "x1": 873, "y1": 0, "x2": 900, "y2": 30, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1405, "y1": 0, "x2": 1438, "y2": 18, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1550, "y1": 13, "x2": 1583, "y2": 31, "logit": 0.0}, "47": {"instance_id": 47, "class_name": "car", "x1": 1121, "y1": 56, "x2": 1159, "y2": 77, "logit": 0.0}}}