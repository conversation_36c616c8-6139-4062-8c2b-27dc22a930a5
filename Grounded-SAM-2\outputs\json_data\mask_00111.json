{"mask_name": "mask_00111.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"13": {"instance_id": 13, "class_name": "car", "x1": 1035, "y1": 270, "x2": 1131, "y2": 376, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 901, "y1": 176, "x2": 975, "y2": 255, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1392, "y1": 101, "x2": 1469, "y2": 154, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1153, "y1": 187, "x2": 1235, "y2": 265, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1513, "y1": 9, "x2": 1556, "y2": 32, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 899, "y1": 97, "x2": 943, "y2": 138, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 848, "y1": 47, "x2": 883, "y2": 76, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 979, "y1": 58, "x2": 1020, "y2": 92, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1277, "y1": 3, "x2": 1308, "y2": 25, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "35": {"instance_id": 35, "class_name": "car", "x1": 878, "y1": 0, "x2": 898, "y2": 25, "logit": 0.0}, "36": {"instance_id": 36, "class_name": "car", "x1": 1543, "y1": 16, "x2": 1577, "y2": 37, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1723, "y1": 33, "x2": 1783, "y2": 66, "logit": 0.0}, "38": {"instance_id": 38, "class_name": "car", "x1": 1709, "y1": 7, "x2": 1721, "y2": 24, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1121, "y1": 0, "x2": 1150, "y2": 19, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "40": {"instance_id": 40, "class_name": "car", "x1": 938, "y1": 0, "x2": 958, "y2": 17, "logit": 0.0}}}