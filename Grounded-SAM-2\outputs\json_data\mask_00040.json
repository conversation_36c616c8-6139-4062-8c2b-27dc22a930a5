{"mask_name": "mask_00040.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"6": {"instance_id": 6, "class_name": "car", "x1": 1048, "y1": 257, "x2": 1155, "y2": 357, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1005, "y1": 109, "x2": 1055, "y2": 155, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1113, "y1": 106, "x2": 1166, "y2": 151, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1405, "y1": 6, "x2": 1436, "y2": 23, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 931, "y1": 14, "x2": 952, "y2": 32, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 994, "y1": 11, "x2": 1015, "y2": 29, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1369, "y1": 0, "x2": 1396, "y2": 18, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1328, "y1": 3, "x2": 1355, "y2": 21, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 914, "y1": 2, "x2": 933, "y2": 14, "logit": 0.0}, "18": {"instance_id": 18, "class_name": "car", "x1": 986, "y1": 2, "x2": 1001, "y2": 19, "logit": 0.0}, "19": {"instance_id": 19, "class_name": "car", "x1": 950, "y1": 3, "x2": 966, "y2": 17, "logit": 0.0}}}