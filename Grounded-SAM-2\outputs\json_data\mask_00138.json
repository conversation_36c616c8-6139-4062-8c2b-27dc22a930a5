{"mask_name": "mask_00138.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"31": {"instance_id": 31, "class_name": "car", "x1": 649, "y1": 409, "x2": 815, "y2": 596, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1021, "y1": 185, "x2": 1106, "y2": 267, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 762, "y1": 143, "x2": 833, "y2": 208, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1599, "y1": 17, "x2": 1645, "y2": 39, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1438, "y1": 0, "x2": 1476, "y2": 25, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 863, "y1": 33, "x2": 891, "y2": 60, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1293, "y1": 34, "x2": 1343, "y2": 72, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1278, "y1": 56, "x2": 1333, "y2": 93, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 967, "y1": 26, "x2": 993, "y2": 50, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 955, "y1": 14, "x2": 981, "y2": 33, "logit": 0.0}}}