{"mask_name": "mask_00033.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"6": {"instance_id": 6, "class_name": "car", "x1": 1036, "y1": 178, "x2": 1112, "y2": 246, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1087, "y1": 695, "x2": 1327, "y2": 1031, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1432, "y1": 662, "x2": 1757, "y2": 1021, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 999, "y1": 86, "x2": 1041, "y2": 124, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1090, "y1": 82, "x2": 1137, "y2": 120, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1457, "y1": 16, "x2": 1491, "y2": 32, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 930, "y1": 12, "x2": 949, "y2": 29, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 989, "y1": 8, "x2": 1008, "y2": 25, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1230, "y1": 0, "x2": 1253, "y2": 9, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1409, "y1": 10, "x2": 1441, "y2": 26, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1378, "y1": 11, "x2": 1409, "y2": 31, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 909, "y1": 2, "x2": 927, "y2": 13, "logit": 0.0}, "18": {"instance_id": 18, "class_name": "car", "x1": 977, "y1": 0, "x2": 992, "y2": 15, "logit": 0.0}, "19": {"instance_id": 19, "class_name": "car", "x1": 949, "y1": 0, "x2": 963, "y2": 13, "logit": 0.0}}}