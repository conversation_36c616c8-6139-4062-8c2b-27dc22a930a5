{"mask_name": "mask_00029.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"6": {"instance_id": 6, "class_name": "car", "x1": 1032, "y1": 152, "x2": 1100, "y2": 210, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1067, "y1": 472, "x2": 1234, "y2": 673, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1320, "y1": 457, "x2": 1519, "y2": 648, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 997, "y1": 78, "x2": 1035, "y2": 111, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1081, "y1": 77, "x2": 1119, "y2": 107, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1485, "y1": 19, "x2": 1520, "y2": 37, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 927, "y1": 11, "x2": 948, "y2": 26, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 985, "y1": 9, "x2": 1005, "y2": 23, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1244, "y1": 0, "x2": 1271, "y2": 13, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1437, "y1": 14, "x2": 1469, "y2": 30, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1406, "y1": 16, "x2": 1436, "y2": 35, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 906, "y1": 3, "x2": 926, "y2": 13, "logit": 0.0}, "18": {"instance_id": 18, "class_name": "car", "x1": 972, "y1": 0, "x2": 988, "y2": 14, "logit": 0.0}, "19": {"instance_id": 19, "class_name": "car", "x1": 947, "y1": 0, "x2": 957, "y2": 13, "logit": 0.0}}}