{"mask_name": "mask_00032.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"6": {"instance_id": 6, "class_name": "car", "x1": 1036, "y1": 174, "x2": 1109, "y2": 238, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1082, "y1": 645, "x2": 1307, "y2": 946, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1408, "y1": 618, "x2": 1700, "y2": 934, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 999, "y1": 83, "x2": 1040, "y2": 122, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1089, "y1": 81, "x2": 1135, "y2": 117, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1462, "y1": 16, "x2": 1495, "y2": 34, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 929, "y1": 12, "x2": 949, "y2": 28, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 989, "y1": 8, "x2": 1008, "y2": 24, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1232, "y1": 0, "x2": 1258, "y2": 9, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1413, "y1": 10, "x2": 1445, "y2": 26, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1382, "y1": 12, "x2": 1413, "y2": 31, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 908, "y1": 3, "x2": 927, "y2": 13, "logit": 0.0}, "18": {"instance_id": 18, "class_name": "car", "x1": 976, "y1": 0, "x2": 991, "y2": 15, "logit": 0.0}, "19": {"instance_id": 19, "class_name": "car", "x1": 948, "y1": 0, "x2": 960, "y2": 12, "logit": 0.0}}}