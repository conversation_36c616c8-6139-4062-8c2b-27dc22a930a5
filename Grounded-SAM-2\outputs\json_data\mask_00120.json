{"mask_name": "mask_00120.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"13": {"instance_id": 13, "class_name": "car", "x1": 1064, "y1": 602, "x2": 1266, "y2": 891, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 886, "y1": 284, "x2": 1005, "y2": 412, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1557, "y1": 163, "x2": 1671, "y2": 242, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1285, "y1": 384, "x2": 1447, "y2": 559, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1437, "y1": 0, "x2": 1477, "y2": 25, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 873, "y1": 140, "x2": 934, "y2": 200, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 832, "y1": 67, "x2": 874, "y2": 102, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 990, "y1": 82, "x2": 1039, "y2": 124, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1219, "y1": 0, "x2": 1246, "y2": 12, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1174, "y1": 18, "x2": 1214, "y2": 47, "logit": 0.0}, "35": {"instance_id": 35, "class_name": "car", "x1": 874, "y1": 0, "x2": 897, "y2": 33, "logit": 0.0}, "36": {"instance_id": 36, "class_name": "car", "x1": 1438, "y1": 0, "x2": 1477, "y2": 25, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1607, "y1": 19, "x2": 1655, "y2": 47, "logit": 0.0}, "38": {"instance_id": 38, "class_name": "car", "x1": 1706, "y1": 6, "x2": 1721, "y2": 21, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1171, "y1": 6, "x2": 1197, "y2": 29, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "40": {"instance_id": 40, "class_name": "car", "x1": 946, "y1": 0, "x2": 968, "y2": 25, "logit": 0.0}}}