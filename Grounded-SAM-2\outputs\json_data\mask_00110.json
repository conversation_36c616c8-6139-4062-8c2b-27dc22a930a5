{"mask_name": "mask_00110.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"13": {"instance_id": 13, "class_name": "car", "x1": 1033, "y1": 249, "x2": 1121, "y2": 345, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 901, "y1": 166, "x2": 971, "y2": 241, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1377, "y1": 94, "x2": 1448, "y2": 145, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1143, "y1": 174, "x2": 1219, "y2": 243, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1524, "y1": 10, "x2": 1570, "y2": 37, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 902, "y1": 92, "x2": 943, "y2": 132, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 851, "y1": 45, "x2": 885, "y2": 74, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 976, "y1": 56, "x2": 1016, "y2": 89, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1284, "y1": 4, "x2": 1316, "y2": 25, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1120, "y1": 0, "x2": 1147, "y2": 19, "logit": 0.0}, "35": {"instance_id": 35, "class_name": "car", "x1": 878, "y1": 0, "x2": 899, "y2": 24, "logit": 0.0}, "36": {"instance_id": 36, "class_name": "car", "x1": 1563, "y1": 16, "x2": 1597, "y2": 41, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1741, "y1": 35, "x2": 1802, "y2": 69, "logit": 0.0}, "38": {"instance_id": 38, "class_name": "car", "x1": 1709, "y1": 7, "x2": 1721, "y2": 24, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1116, "y1": 0, "x2": 1146, "y2": 19, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "40": {"instance_id": 40, "class_name": "car", "x1": 938, "y1": 0, "x2": 957, "y2": 16, "logit": 0.0}}}