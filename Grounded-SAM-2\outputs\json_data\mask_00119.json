{"mask_name": "mask_00119.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"13": {"instance_id": 13, "class_name": "car", "x1": 1056, "y1": 524, "x2": 1234, "y2": 765, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 889, "y1": 263, "x2": 1001, "y2": 385, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1530, "y1": 152, "x2": 1636, "y2": 225, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1258, "y1": 339, "x2": 1403, "y2": 495, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1448, "y1": 0, "x2": 1490, "y2": 27, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 876, "y1": 132, "x2": 934, "y2": 190, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 834, "y1": 64, "x2": 877, "y2": 98, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 987, "y1": 78, "x2": 1037, "y2": 119, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1227, "y1": 0, "x2": 1254, "y2": 14, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1172, "y1": 18, "x2": 1208, "y2": 45, "logit": 0.0}, "35": {"instance_id": 35, "class_name": "car", "x1": 874, "y1": 0, "x2": 896, "y2": 32, "logit": 0.0}, "36": {"instance_id": 36, "class_name": "car", "x1": 1449, "y1": 0, "x2": 1490, "y2": 26, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1622, "y1": 20, "x2": 1670, "y2": 50, "logit": 0.0}, "38": {"instance_id": 38, "class_name": "car", "x1": 1705, "y1": 6, "x2": 1721, "y2": 22, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1169, "y1": 5, "x2": 1192, "y2": 28, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "40": {"instance_id": 40, "class_name": "car", "x1": 946, "y1": 0, "x2": 967, "y2": 24, "logit": 0.0}}}