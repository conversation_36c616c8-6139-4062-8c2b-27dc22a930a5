{"mask_name": "mask_00038.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"6": {"instance_id": 6, "class_name": "car", "x1": 1045, "y1": 234, "x2": 1141, "y2": 323, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1004, "y1": 102, "x2": 1049, "y2": 144, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1106, "y1": 100, "x2": 1155, "y2": 142, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1419, "y1": 9, "x2": 1450, "y2": 25, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 930, "y1": 13, "x2": 951, "y2": 31, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 993, "y1": 10, "x2": 1014, "y2": 27, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1378, "y1": 5, "x2": 1407, "y2": 20, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1340, "y1": 7, "x2": 1367, "y2": 25, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 911, "y1": 0, "x2": 932, "y2": 14, "logit": 0.0}, "18": {"instance_id": 18, "class_name": "car", "x1": 984, "y1": 0, "x2": 999, "y2": 17, "logit": 0.0}, "19": {"instance_id": 19, "class_name": "car", "x1": 948, "y1": 3, "x2": 964, "y2": 17, "logit": 0.0}}}