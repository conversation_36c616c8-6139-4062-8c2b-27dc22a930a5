{"mask_name": "mask_00156.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"30": {"instance_id": 30, "class_name": "car", "x1": 431, "y1": 446, "x2": 640, "y2": 659, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1078, "y1": 785, "x2": 1406, "y2": 1079, "logit": 0.0}, "45": {"instance_id": 45, "class_name": "car", "x1": 1554, "y1": 10, "x2": 1600, "y2": 32, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 842, "y1": 70, "x2": 879, "y2": 105, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1487, "y1": 134, "x2": 1589, "y2": 202, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 984, "y1": 35, "x2": 1016, "y2": 62, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1580, "y1": 111, "x2": 1683, "y2": 185, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 990, "y1": 59, "x2": 1025, "y2": 94, "logit": 0.0}, "46": {"instance_id": 46, "class_name": "car", "x1": 867, "y1": 7, "x2": 902, "y2": 44, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1321, "y1": 0, "x2": 1350, "y2": 8, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1433, "y1": 0, "x2": 1466, "y2": 15, "logit": 0.0}, "47": {"instance_id": 47, "class_name": "car", "x1": 1121, "y1": 55, "x2": 1160, "y2": 76, "logit": 0.0}}}