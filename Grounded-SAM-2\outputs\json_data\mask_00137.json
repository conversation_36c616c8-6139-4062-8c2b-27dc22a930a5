{"mask_name": "mask_00137.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"31": {"instance_id": 31, "class_name": "car", "x1": 686, "y1": 368, "x2": 834, "y2": 530, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1019, "y1": 175, "x2": 1098, "y2": 247, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 769, "y1": 137, "x2": 836, "y2": 196, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1614, "y1": 18, "x2": 1661, "y2": 40, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1308, "y1": 0, "x2": 1335, "y2": 6, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1448, "y1": 3, "x2": 1486, "y2": 27, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 863, "y1": 32, "x2": 891, "y2": 58, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1283, "y1": 32, "x2": 1331, "y2": 69, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1268, "y1": 53, "x2": 1322, "y2": 90, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 965, "y1": 24, "x2": 992, "y2": 48, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 955, "y1": 12, "x2": 978, "y2": 31, "logit": 0.0}}}