{"mask_name": "mask_00132.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"31": {"instance_id": 31, "class_name": "car", "x1": 777, "y1": 265, "x2": 883, "y2": 369, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 807, "y1": 849, "x2": 1083, "y2": 1079, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1010, "y1": 137, "x2": 1075, "y2": 198, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 797, "y1": 111, "x2": 851, "y2": 159, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1671, "y1": 25, "x2": 1722, "y2": 50, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1344, "y1": 0, "x2": 1385, "y2": 11, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1487, "y1": 7, "x2": 1528, "y2": 33, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 866, "y1": 27, "x2": 893, "y2": 50, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1241, "y1": 21, "x2": 1283, "y2": 48, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1236, "y1": 42, "x2": 1283, "y2": 75, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 960, "y1": 18, "x2": 984, "y2": 40, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 950, "y1": 7, "x2": 971, "y2": 25, "logit": 0.0}}}