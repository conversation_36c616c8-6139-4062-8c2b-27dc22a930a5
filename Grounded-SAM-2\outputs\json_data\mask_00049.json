{"mask_name": "mask_00049.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"10": {"instance_id": 10, "class_name": "car", "x1": 1153, "y1": 159, "x2": 1226, "y2": 225, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1009, "y1": 161, "x2": 1075, "y2": 225, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1074, "y1": 506, "x2": 1274, "y2": 747, "logit": 0.0}, "20": {"instance_id": 20, "class_name": "car", "x1": 1315, "y1": 75, "x2": 1370, "y2": 116, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 929, "y1": 17, "x2": 952, "y2": 40, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1273, "y1": 0, "x2": 1295, "y2": 13, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1310, "y1": 0, "x2": 1343, "y2": 11, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 954, "y1": 6, "x2": 972, "y2": 22, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1000, "y1": 16, "x2": 1022, "y2": 37, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1344, "y1": 0, "x2": 1373, "y2": 16, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 995, "y1": 9, "x2": 1013, "y2": 26, "logit": 0.0}, "23": {"instance_id": 23, "class_name": "car", "x1": 1144, "y1": 66, "x2": 1178, "y2": 94, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 921, "y1": 3, "x2": 942, "y2": 20, "logit": 0.0}, "24": {"instance_id": 24, "class_name": "car", "x1": 896, "y1": 0, "x2": 910, "y2": 10, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1082, "y1": 0, "x2": 1107, "y2": 19, "logit": 0.0}}}