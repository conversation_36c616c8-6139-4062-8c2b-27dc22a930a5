{"mask_name": "mask_00131.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"31": {"instance_id": 31, "class_name": "car", "x1": 793, "y1": 245, "x2": 892, "y2": 345, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 819, "y1": 724, "x2": 1095, "y2": 1079, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1007, "y1": 127, "x2": 1071, "y2": 191, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 799, "y1": 105, "x2": 856, "y2": 152, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1687, "y1": 23, "x2": 1743, "y2": 53, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1352, "y1": 0, "x2": 1394, "y2": 13, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1498, "y1": 9, "x2": 1538, "y2": 36, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 867, "y1": 21, "x2": 893, "y2": 49, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1232, "y1": 20, "x2": 1274, "y2": 45, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1228, "y1": 40, "x2": 1276, "y2": 72, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 958, "y1": 17, "x2": 982, "y2": 39, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 947, "y1": 6, "x2": 969, "y2": 24, "logit": 0.0}}}