{"mask_name": "mask_00091.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"22": {"instance_id": 22, "class_name": "car", "x1": 1060, "y1": 64, "x2": 1101, "y2": 98, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1013, "y1": 94, "x2": 1056, "y2": 132, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1463, "y1": 32, "x2": 1511, "y2": 61, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1357, "y1": 0, "x2": 1412, "y2": 18, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1223, "y1": 39, "x2": 1264, "y2": 68, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 909, "y1": 75, "x2": 950, "y2": 119, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 873, "y1": 13, "x2": 898, "y2": 40, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 930, "y1": 44, "x2": 960, "y2": 72, "logit": 0.0}, "28": {"instance_id": 28, "class_name": "car", "x1": 873, "y1": 15, "x2": 897, "y2": 40, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 956, "y1": 27, "x2": 985, "y2": 50, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1086, "y1": 0, "x2": 1112, "y2": 15, "logit": 0.0}, "33": {"instance_id": 33, "class_name": "car", "x1": 873, "y1": 16, "x2": 897, "y2": 40, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 930, "y1": 44, "x2": 961, "y2": 73, "logit": 0.0}}}