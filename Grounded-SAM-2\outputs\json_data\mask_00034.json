{"mask_name": "mask_00034.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"6": {"instance_id": 6, "class_name": "car", "x1": 1038, "y1": 189, "x2": 1117, "y2": 261, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1093, "y1": 818, "x2": 1380, "y2": 1079, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1490, "y1": 772, "x2": 1861, "y2": 1079, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1000, "y1": 90, "x2": 1044, "y2": 130, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1093, "y1": 84, "x2": 1138, "y2": 125, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1448, "y1": 14, "x2": 1481, "y2": 31, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 929, "y1": 12, "x2": 949, "y2": 29, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 990, "y1": 8, "x2": 1008, "y2": 26, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1226, "y1": 0, "x2": 1248, "y2": 7, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1403, "y1": 9, "x2": 1431, "y2": 24, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1369, "y1": 11, "x2": 1399, "y2": 31, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 909, "y1": 2, "x2": 929, "y2": 14, "logit": 0.0}, "18": {"instance_id": 18, "class_name": "car", "x1": 977, "y1": 0, "x2": 993, "y2": 16, "logit": 0.0}, "19": {"instance_id": 19, "class_name": "car", "x1": 948, "y1": 0, "x2": 963, "y2": 14, "logit": 0.0}}}