{"mask_name": "mask_00157.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"30": {"instance_id": 30, "class_name": "car", "x1": 402, "y1": 470, "x2": 624, "y2": 703, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1093, "y1": 856, "x2": 1396, "y2": 1079, "logit": 0.0}, "45": {"instance_id": 45, "class_name": "car", "x1": 1548, "y1": 10, "x2": 1593, "y2": 36, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 841, "y1": 72, "x2": 878, "y2": 107, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1498, "y1": 137, "x2": 1603, "y2": 209, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 984, "y1": 36, "x2": 1017, "y2": 64, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1596, "y1": 115, "x2": 1703, "y2": 191, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 991, "y1": 60, "x2": 1026, "y2": 96, "logit": 0.0}, "46": {"instance_id": 46, "class_name": "car", "x1": 867, "y1": 9, "x2": 901, "y2": 45, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1319, "y1": 0, "x2": 1346, "y2": 7, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1429, "y1": 0, "x2": 1461, "y2": 15, "logit": 0.0}, "47": {"instance_id": 47, "class_name": "car", "x1": 1121, "y1": 55, "x2": 1160, "y2": 76, "logit": 0.0}}}