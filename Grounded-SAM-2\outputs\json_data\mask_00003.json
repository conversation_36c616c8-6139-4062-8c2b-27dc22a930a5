{"mask_name": "mask_00003.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "car", "x1": 1110, "y1": 21, "x2": 1134, "y2": 40, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1089, "y1": 114, "x2": 1143, "y2": 158, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1016, "y1": 118, "x2": 1064, "y2": 164, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1746, "y1": 63, "x2": 1801, "y2": 89, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 969, "y1": 40, "x2": 993, "y2": 62, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 994, "y1": 66, "x2": 1031, "y2": 96, "logit": 0.0}, "7": {"instance_id": 7, "class_name": "car", "x1": 1267, "y1": 11, "x2": 1295, "y2": 26, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1679, "y1": 61, "x2": 1728, "y2": 89, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1357, "y1": 15, "x2": 1392, "y2": 38, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1021, "y1": 38, "x2": 1045, "y2": 58, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 912, "y1": 9, "x2": 928, "y2": 19, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1660, "y1": 53, "x2": 1691, "y2": 75, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 949, "y1": 8, "x2": 963, "y2": 16, "logit": 0.0}, "14": {"instance_id": 14, "class_name": "car", "x1": 1659, "y1": 52, "x2": 1692, "y2": 76, "logit": 0.0}, "15": {"instance_id": 15, "class_name": "car", "x1": 1163, "y1": 0, "x2": 1176, "y2": 18, "logit": 0.0}, "16": {"instance_id": 16, "class_name": "car", "x1": 981, "y1": 0, "x2": 999, "y2": 8, "logit": 0.0}}}