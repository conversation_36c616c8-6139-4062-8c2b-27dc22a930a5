{"mask_name": "mask_00105.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"13": {"instance_id": 13, "class_name": "car", "x1": 1024, "y1": 187, "x2": 1095, "y2": 255, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 905, "y1": 133, "x2": 964, "y2": 196, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1324, "y1": 75, "x2": 1387, "y2": 118, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1112, "y1": 131, "x2": 1173, "y2": 186, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1583, "y1": 15, "x2": 1626, "y2": 38, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 912, "y1": 77, "x2": 950, "y2": 116, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 860, "y1": 33, "x2": 889, "y2": 62, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 972, "y1": 47, "x2": 1008, "y2": 76, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1321, "y1": 11, "x2": 1357, "y2": 32, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1120, "y1": 8, "x2": 1144, "y2": 28, "logit": 0.0}, "35": {"instance_id": 35, "class_name": "car", "x1": 879, "y1": 0, "x2": 900, "y2": 19, "logit": 0.0}, "36": {"instance_id": 36, "class_name": "car", "x1": 1623, "y1": 21, "x2": 1669, "y2": 48, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1820, "y1": 44, "x2": 1891, "y2": 79, "logit": 0.0}, "38": {"instance_id": 38, "class_name": "car", "x1": 1711, "y1": 7, "x2": 1723, "y2": 23, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1098, "y1": 0, "x2": 1125, "y2": 14, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1297, "y1": 0, "x2": 1324, "y2": 5, "logit": 0.0}, "40": {"instance_id": 40, "class_name": "car", "x1": 934, "y1": 0, "x2": 952, "y2": 12, "logit": 0.0}}}