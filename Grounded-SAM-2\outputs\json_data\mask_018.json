{"mask_name": "mask_018.npy", "mask_height": 240, "mask_width": 360, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "bike", "x1": 98, "y1": 128, "x2": 133, "y2": 147, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "bike", "x1": 98, "y1": 128, "x2": 133, "y2": 147, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "bike", "x1": 119, "y1": 134, "x2": 133, "y2": 146, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "bike", "x1": 99, "y1": 133, "x2": 133, "y2": 146, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "bike", "x1": 202, "y1": 223, "x2": 207, "y2": 234, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "bike", "x1": 194, "y1": 226, "x2": 204, "y2": 239, "logit": 0.0}}}