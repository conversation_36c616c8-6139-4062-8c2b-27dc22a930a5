{"mask_name": "mask_00094.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"22": {"instance_id": 22, "class_name": "car", "x1": 1068, "y1": 72, "x2": 1112, "y2": 112, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1014, "y1": 107, "x2": 1062, "y2": 151, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1428, "y1": 27, "x2": 1473, "y2": 54, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1331, "y1": 0, "x2": 1392, "y2": 15, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1239, "y1": 46, "x2": 1283, "y2": 76, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 908, "y1": 82, "x2": 951, "y2": 129, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 868, "y1": 16, "x2": 896, "y2": 44, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 926, "y1": 50, "x2": 958, "y2": 80, "logit": 0.0}, "28": {"instance_id": 28, "class_name": "car", "x1": 869, "y1": 22, "x2": 896, "y2": 44, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 959, "y1": 32, "x2": 989, "y2": 55, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1091, "y1": 0, "x2": 1119, "y2": 18, "logit": 0.0}, "33": {"instance_id": 33, "class_name": "car", "x1": 869, "y1": 22, "x2": 896, "y2": 44, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 926, "y1": 50, "x2": 959, "y2": 80, "logit": 0.0}}}