{"mask_name": "mask_00042.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"10": {"instance_id": 10, "class_name": "car", "x1": 1120, "y1": 114, "x2": 1174, "y2": 164, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1006, "y1": 118, "x2": 1058, "y2": 167, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1051, "y1": 289, "x2": 1168, "y2": 404, "logit": 0.0}, "20": {"instance_id": 20, "class_name": "car", "x1": 1265, "y1": 57, "x2": 1310, "y2": 92, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 931, "y1": 15, "x2": 953, "y2": 33, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1319, "y1": 0, "x2": 1344, "y2": 20, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1357, "y1": 0, "x2": 1386, "y2": 18, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 950, "y1": 5, "x2": 967, "y2": 19, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 997, "y1": 14, "x2": 1018, "y2": 31, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1393, "y1": 4, "x2": 1424, "y2": 22, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 987, "y1": 4, "x2": 1006, "y2": 21, "logit": 0.0}, "23": {"instance_id": 23, "class_name": "car", "x1": 1144, "y1": 68, "x2": 1181, "y2": 96, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 916, "y1": 0, "x2": 934, "y2": 17, "logit": 0.0}, "24": {"instance_id": 24, "class_name": "car", "x1": 894, "y1": 0, "x2": 910, "y2": 9, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1066, "y1": 0, "x2": 1089, "y2": 16, "logit": 0.0}}}