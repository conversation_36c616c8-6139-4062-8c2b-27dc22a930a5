{"mask_name": "mask_00045.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"10": {"instance_id": 10, "class_name": "car", "x1": 1132, "y1": 131, "x2": 1195, "y2": 186, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1006, "y1": 129, "x2": 1064, "y2": 188, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1057, "y1": 356, "x2": 1201, "y2": 498, "logit": 0.0}, "20": {"instance_id": 20, "class_name": "car", "x1": 1283, "y1": 64, "x2": 1332, "y2": 101, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 930, "y1": 15, "x2": 953, "y2": 36, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1298, "y1": 0, "x2": 1322, "y2": 17, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1341, "y1": 0, "x2": 1368, "y2": 15, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 951, "y1": 5, "x2": 970, "y2": 21, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 997, "y1": 15, "x2": 1019, "y2": 33, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1372, "y1": 0, "x2": 1403, "y2": 20, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 992, "y1": 7, "x2": 1008, "y2": 21, "logit": 0.0}, "23": {"instance_id": 23, "class_name": "car", "x1": 1145, "y1": 67, "x2": 1179, "y2": 95, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 916, "y1": 2, "x2": 938, "y2": 17, "logit": 0.0}, "24": {"instance_id": 24, "class_name": "car", "x1": 895, "y1": 0, "x2": 910, "y2": 9, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1074, "y1": 0, "x2": 1097, "y2": 16, "logit": 0.0}}}