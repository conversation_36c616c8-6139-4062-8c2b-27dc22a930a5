{"mask_name": "mask_00044.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"10": {"instance_id": 10, "class_name": "car", "x1": 1130, "y1": 127, "x2": 1190, "y2": 181, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1007, "y1": 128, "x2": 1063, "y2": 184, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1055, "y1": 343, "x2": 1194, "y2": 477, "logit": 0.0}, "20": {"instance_id": 20, "class_name": "car", "x1": 1280, "y1": 64, "x2": 1328, "y2": 99, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 931, "y1": 15, "x2": 953, "y2": 35, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1304, "y1": 0, "x2": 1326, "y2": 19, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1345, "y1": 0, "x2": 1374, "y2": 16, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 951, "y1": 4, "x2": 970, "y2": 21, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 998, "y1": 15, "x2": 1019, "y2": 33, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1377, "y1": 2, "x2": 1407, "y2": 20, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 990, "y1": 6, "x2": 1008, "y2": 21, "logit": 0.0}, "23": {"instance_id": 23, "class_name": "car", "x1": 1145, "y1": 67, "x2": 1179, "y2": 95, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 917, "y1": 0, "x2": 938, "y2": 17, "logit": 0.0}, "24": {"instance_id": 24, "class_name": "car", "x1": 895, "y1": 0, "x2": 910, "y2": 10, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1073, "y1": 0, "x2": 1096, "y2": 17, "logit": 0.0}}}