{"mask_name": "mask_012.npy", "mask_height": 240, "mask_width": 360, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "bike", "x1": 87, "y1": 131, "x2": 122, "y2": 146, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "bike", "x1": 86, "y1": 129, "x2": 122, "y2": 146, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "bike", "x1": 109, "y1": 134, "x2": 122, "y2": 146, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "bike", "x1": 88, "y1": 132, "x2": 122, "y2": 146, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "bike", "x1": 198, "y1": 229, "x2": 203, "y2": 239, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "bike", "x1": 190, "y1": 230, "x2": 200, "y2": 239, "logit": 0.0}}}