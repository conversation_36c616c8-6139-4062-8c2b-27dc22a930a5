{"mask_name": "mask_00147.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"30": {"instance_id": 30, "class_name": "car", "x1": 678, "y1": 233, "x2": 779, "y2": 329, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1043, "y1": 323, "x2": 1179, "y2": 465, "logit": 0.0}, "45": {"instance_id": 45, "class_name": "car", "x1": 1655, "y1": 20, "x2": 1706, "y2": 46, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 856, "y1": 48, "x2": 888, "y2": 80, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1359, "y1": 83, "x2": 1432, "y2": 134, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 970, "y1": 20, "x2": 999, "y2": 46, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1402, "y1": 62, "x2": 1470, "y2": 112, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 979, "y1": 38, "x2": 1010, "y2": 69, "logit": 0.0}, "46": {"instance_id": 46, "class_name": "car", "x1": 873, "y1": 0, "x2": 902, "y2": 34, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1376, "y1": 0, "x2": 1407, "y2": 15, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1506, "y1": 7, "x2": 1548, "y2": 27, "logit": 0.0}, "47": {"instance_id": 47, "class_name": "car", "x1": 1121, "y1": 56, "x2": 1159, "y2": 76, "logit": 0.0}}}