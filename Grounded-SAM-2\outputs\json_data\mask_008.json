{"mask_name": "mask_008.npy", "mask_height": 240, "mask_width": 360, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "bike", "x1": 84, "y1": 128, "x2": 115, "y2": 146, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "bike", "x1": 84, "y1": 127, "x2": 115, "y2": 146, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "bike", "x1": 101, "y1": 133, "x2": 115, "y2": 146, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "bike", "x1": 84, "y1": 133, "x2": 115, "y2": 146, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "bike", "x1": 196, "y1": 233, "x2": 200, "y2": 239, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "bike", "x1": 188, "y1": 235, "x2": 197, "y2": 239, "logit": 0.0}}}