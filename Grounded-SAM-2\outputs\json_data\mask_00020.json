{"mask_name": "mask_00020.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1193, "y1": 239, "x2": 1293, "y2": 326, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1046, "y1": 250, "x2": 1135, "y2": 346, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1563, "y1": 32, "x2": 1601, "y2": 56, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 990, "y1": 61, "x2": 1022, "y2": 88, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1020, "y1": 108, "x2": 1071, "y2": 151, "logit": 0.0}, "7": {"instance_id": 7, "class_name": "car", "x1": 1204, "y1": 0, "x2": 1227, "y2": 8, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1483, "y1": 27, "x2": 1523, "y2": 50, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1282, "y1": 0, "x2": 1315, "y2": 20, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1060, "y1": 58, "x2": 1092, "y2": 84, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 926, "y1": 6, "x2": 944, "y2": 23, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1509, "y1": 24, "x2": 1540, "y2": 43, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 977, "y1": 4, "x2": 993, "y2": 19, "logit": 0.0}, "14": {"instance_id": 14, "class_name": "car", "x1": 1516, "y1": 24, "x2": 1540, "y2": 42, "logit": 0.0}, "15": {"instance_id": 15, "class_name": "car", "x1": 1170, "y1": 0, "x2": 1182, "y2": 14, "logit": 0.0}, "16": {"instance_id": 16, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}}}