{"mask_name": "mask_00159.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"30": {"instance_id": 30, "class_name": "car", "x1": 234, "y1": 601, "x2": 536, "y2": 935, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "45": {"instance_id": 45, "class_name": "car", "x1": 1529, "y1": 6, "x2": 1573, "y2": 33, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 835, "y1": 78, "x2": 875, "y2": 116, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1548, "y1": 157, "x2": 1665, "y2": 237, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 986, "y1": 42, "x2": 1022, "y2": 70, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1671, "y1": 137, "x2": 1796, "y2": 225, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 994, "y1": 66, "x2": 1032, "y2": 103, "logit": 0.0}, "46": {"instance_id": 46, "class_name": "car", "x1": 865, "y1": 11, "x2": 898, "y2": 48, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1307, "y1": 0, "x2": 1333, "y2": 4, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1410, "y1": 0, "x2": 1443, "y2": 10, "logit": 0.0}, "47": {"instance_id": 47, "class_name": "car", "x1": 1121, "y1": 55, "x2": 1160, "y2": 75, "logit": 0.0}}}