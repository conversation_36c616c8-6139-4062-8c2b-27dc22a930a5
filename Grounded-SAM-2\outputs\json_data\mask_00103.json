{"mask_name": "mask_00103.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"13": {"instance_id": 13, "class_name": "car", "x1": 1022, "y1": 162, "x2": 1087, "y2": 227, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 907, "y1": 119, "x2": 960, "y2": 177, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1304, "y1": 66, "x2": 1360, "y2": 107, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1101, "y1": 111, "x2": 1157, "y2": 165, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1603, "y1": 17, "x2": 1657, "y2": 40, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 915, "y1": 66, "x2": 953, "y2": 105, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 862, "y1": 32, "x2": 892, "y2": 57, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 970, "y1": 43, "x2": 1005, "y2": 71, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1341, "y1": 15, "x2": 1377, "y2": 34, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1116, "y1": 4, "x2": 1144, "y2": 25, "logit": 0.0}, "35": {"instance_id": 35, "class_name": "car", "x1": 881, "y1": 0, "x2": 901, "y2": 18, "logit": 0.0}, "36": {"instance_id": 36, "class_name": "car", "x1": 1659, "y1": 25, "x2": 1708, "y2": 53, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1866, "y1": 48, "x2": 1919, "y2": 85, "logit": 0.0}, "38": {"instance_id": 38, "class_name": "car", "x1": 1711, "y1": 7, "x2": 1724, "y2": 21, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1089, "y1": 0, "x2": 1117, "y2": 13, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1312, "y1": 0, "x2": 1337, "y2": 7, "logit": 0.0}, "40": {"instance_id": 40, "class_name": "car", "x1": 932, "y1": 0, "x2": 951, "y2": 10, "logit": 0.0}}}