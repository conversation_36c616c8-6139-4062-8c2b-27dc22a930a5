{"mask_name": "mask_00005.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "car", "x1": 1117, "y1": 21, "x2": 1141, "y2": 41, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1099, "y1": 123, "x2": 1155, "y2": 169, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1018, "y1": 128, "x2": 1069, "y2": 178, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1716, "y1": 57, "x2": 1767, "y2": 82, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 971, "y1": 41, "x2": 997, "y2": 64, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 997, "y1": 70, "x2": 1036, "y2": 100, "logit": 0.0}, "7": {"instance_id": 7, "class_name": "car", "x1": 1257, "y1": 9, "x2": 1282, "y2": 23, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1645, "y1": 56, "x2": 1692, "y2": 82, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1345, "y1": 14, "x2": 1379, "y2": 35, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1025, "y1": 40, "x2": 1050, "y2": 60, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 913, "y1": 9, "x2": 929, "y2": 19, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1633, "y1": 50, "x2": 1659, "y2": 68, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 953, "y1": 7, "x2": 967, "y2": 16, "logit": 0.0}, "14": {"instance_id": 14, "class_name": "car", "x1": 1633, "y1": 48, "x2": 1663, "y2": 68, "logit": 0.0}, "15": {"instance_id": 15, "class_name": "car", "x1": 1166, "y1": 0, "x2": 1175, "y2": 8, "logit": 0.0}, "16": {"instance_id": 16, "class_name": "car", "x1": 988, "y1": 0, "x2": 1004, "y2": 7, "logit": 0.0}}}