{"mask_name": "mask_00012.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "car", "x1": 1141, "y1": 25, "x2": 1168, "y2": 45, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1132, "y1": 159, "x2": 1202, "y2": 218, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1030, "y1": 167, "x2": 1091, "y2": 228, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1644, "y1": 45, "x2": 1689, "y2": 67, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 981, "y1": 49, "x2": 1009, "y2": 73, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1008, "y1": 81, "x2": 1052, "y2": 119, "logit": 0.0}, "7": {"instance_id": 7, "class_name": "car", "x1": 1234, "y1": 4, "x2": 1258, "y2": 18, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1568, "y1": 41, "x2": 1610, "y2": 65, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1316, "y1": 6, "x2": 1349, "y2": 28, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1039, "y1": 46, "x2": 1069, "y2": 69, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 921, "y1": 7, "x2": 937, "y2": 21, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 964, "y1": 3, "x2": 979, "y2": 17, "logit": 0.0}, "14": {"instance_id": 14, "class_name": "car", "x1": 1578, "y1": 37, "x2": 1615, "y2": 53, "logit": 0.0}, "15": {"instance_id": 15, "class_name": "car", "x1": 1170, "y1": 0, "x2": 1177, "y2": 17, "logit": 0.0}, "16": {"instance_id": 16, "class_name": "car", "x1": 1005, "y1": 0, "x2": 1021, "y2": 7, "logit": 0.0}}}