{"mask_name": "mask_00095.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"22": {"instance_id": 22, "class_name": "car", "x1": 1071, "y1": 77, "x2": 1116, "y2": 118, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1015, "y1": 113, "x2": 1064, "y2": 159, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1415, "y1": 26, "x2": 1459, "y2": 51, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1322, "y1": 0, "x2": 1383, "y2": 14, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1246, "y1": 48, "x2": 1292, "y2": 79, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 907, "y1": 88, "x2": 950, "y2": 133, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 867, "y1": 21, "x2": 896, "y2": 46, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 925, "y1": 52, "x2": 957, "y2": 83, "logit": 0.0}, "28": {"instance_id": 28, "class_name": "car", "x1": 867, "y1": 24, "x2": 895, "y2": 46, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 959, "y1": 32, "x2": 990, "y2": 57, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1094, "y1": 0, "x2": 1123, "y2": 19, "logit": 0.0}, "33": {"instance_id": 33, "class_name": "car", "x1": 868, "y1": 24, "x2": 895, "y2": 46, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 925, "y1": 51, "x2": 958, "y2": 83, "logit": 0.0}}}