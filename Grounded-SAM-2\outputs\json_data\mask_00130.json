{"mask_name": "mask_00130.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"31": {"instance_id": 31, "class_name": "car", "x1": 808, "y1": 228, "x2": 899, "y2": 321, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 836, "y1": 625, "x2": 1076, "y2": 993, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 1005, "y1": 123, "x2": 1068, "y2": 179, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 807, "y1": 98, "x2": 860, "y2": 145, "logit": 0.0}, "41": {"instance_id": 41, "class_name": "car", "x1": 1704, "y1": 26, "x2": 1762, "y2": 55, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1360, "y1": 0, "x2": 1403, "y2": 14, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1507, "y1": 9, "x2": 1551, "y2": 38, "logit": 0.0}, "42": {"instance_id": 42, "class_name": "car", "x1": 868, "y1": 19, "x2": 896, "y2": 46, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1222, "y1": 18, "x2": 1263, "y2": 45, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1222, "y1": 38, "x2": 1267, "y2": 68, "logit": 0.0}, "43": {"instance_id": 43, "class_name": "car", "x1": 957, "y1": 15, "x2": 981, "y2": 36, "logit": 0.0}, "44": {"instance_id": 44, "class_name": "car", "x1": 946, "y1": 4, "x2": 966, "y2": 22, "logit": 0.0}}}