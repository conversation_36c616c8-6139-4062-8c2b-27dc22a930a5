{"mask_name": "mask_00002.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "car", "x1": 1108, "y1": 20, "x2": 1133, "y2": 40, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1087, "y1": 112, "x2": 1139, "y2": 155, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1015, "y1": 116, "x2": 1062, "y2": 162, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1755, "y1": 64, "x2": 1809, "y2": 90, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 968, "y1": 40, "x2": 993, "y2": 61, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 994, "y1": 65, "x2": 1030, "y2": 95, "logit": 0.0}, "7": {"instance_id": 7, "class_name": "car", "x1": 1270, "y1": 12, "x2": 1298, "y2": 27, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1687, "y1": 62, "x2": 1737, "y2": 91, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1361, "y1": 16, "x2": 1397, "y2": 39, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1019, "y1": 38, "x2": 1044, "y2": 58, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 912, "y1": 10, "x2": 928, "y2": 19, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1665, "y1": 56, "x2": 1695, "y2": 76, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 948, "y1": 8, "x2": 962, "y2": 16, "logit": 0.0}, "14": {"instance_id": 14, "class_name": "car", "x1": 1664, "y1": 53, "x2": 1699, "y2": 77, "logit": 0.0}, "15": {"instance_id": 15, "class_name": "car", "x1": 1163, "y1": 0, "x2": 1176, "y2": 19, "logit": 0.0}, "16": {"instance_id": 16, "class_name": "car", "x1": 978, "y1": 0, "x2": 998, "y2": 8, "logit": 0.0}}}