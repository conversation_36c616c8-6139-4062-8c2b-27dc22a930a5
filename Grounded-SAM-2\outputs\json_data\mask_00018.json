{"mask_name": "mask_00018.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1172, "y1": 209, "x2": 1262, "y2": 287, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1041, "y1": 219, "x2": 1120, "y2": 299, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1585, "y1": 34, "x2": 1625, "y2": 55, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 988, "y1": 55, "x2": 1020, "y2": 82, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1019, "y1": 98, "x2": 1067, "y2": 139, "logit": 0.0}, "7": {"instance_id": 7, "class_name": "car", "x1": 1215, "y1": 0, "x2": 1236, "y2": 12, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1506, "y1": 30, "x2": 1551, "y2": 53, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1292, "y1": 0, "x2": 1323, "y2": 23, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1055, "y1": 53, "x2": 1086, "y2": 78, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 926, "y1": 4, "x2": 944, "y2": 22, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1535, "y1": 26, "x2": 1564, "y2": 45, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 964, "y1": 4, "x2": 989, "y2": 18, "logit": 0.0}, "14": {"instance_id": 14, "class_name": "car", "x1": 1538, "y1": 25, "x2": 1564, "y2": 43, "logit": 0.0}, "15": {"instance_id": 15, "class_name": "car", "x1": 1171, "y1": 0, "x2": 1181, "y2": 15, "logit": 0.0}, "16": {"instance_id": 16, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}}}