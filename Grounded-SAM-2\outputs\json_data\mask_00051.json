{"mask_name": "mask_00051.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"10": {"instance_id": 10, "class_name": "car", "x1": 1165, "y1": 175, "x2": 1240, "y2": 243, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 1010, "y1": 176, "x2": 1081, "y2": 243, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1086, "y1": 604, "x2": 1329, "y2": 933, "logit": 0.0}, "20": {"instance_id": 20, "class_name": "car", "x1": 1330, "y1": 80, "x2": 1389, "y2": 123, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 930, "y1": 17, "x2": 954, "y2": 42, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1262, "y1": 0, "x2": 1285, "y2": 12, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1307, "y1": 0, "x2": 1331, "y2": 10, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 954, "y1": 4, "x2": 974, "y2": 24, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1001, "y1": 19, "x2": 1023, "y2": 39, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1332, "y1": 0, "x2": 1361, "y2": 14, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 998, "y1": 9, "x2": 1016, "y2": 27, "logit": 0.0}, "23": {"instance_id": 23, "class_name": "car", "x1": 1144, "y1": 66, "x2": 1177, "y2": 93, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 922, "y1": 3, "x2": 943, "y2": 20, "logit": 0.0}, "24": {"instance_id": 24, "class_name": "car", "x1": 896, "y1": 0, "x2": 911, "y2": 11, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1088, "y1": 0, "x2": 1112, "y2": 19, "logit": 0.0}}}