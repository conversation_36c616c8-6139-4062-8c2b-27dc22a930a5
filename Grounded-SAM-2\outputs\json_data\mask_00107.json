{"mask_name": "mask_00107.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"13": {"instance_id": 13, "class_name": "car", "x1": 1028, "y1": 207, "x2": 1104, "y2": 286, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 903, "y1": 144, "x2": 967, "y2": 210, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1344, "y1": 81, "x2": 1407, "y2": 127, "logit": 0.0}, "22": {"instance_id": 22, "class_name": "car", "x1": 1123, "y1": 144, "x2": 1188, "y2": 204, "logit": 0.0}, "34": {"instance_id": 34, "class_name": "car", "x1": 1554, "y1": 14, "x2": 1601, "y2": 34, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 908, "y1": 82, "x2": 949, "y2": 123, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 857, "y1": 39, "x2": 888, "y2": 67, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 973, "y1": 50, "x2": 1012, "y2": 81, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1306, "y1": 8, "x2": 1342, "y2": 29, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1126, "y1": 8, "x2": 1143, "y2": 30, "logit": 0.0}, "35": {"instance_id": 35, "class_name": "car", "x1": 879, "y1": 0, "x2": 899, "y2": 21, "logit": 0.0}, "36": {"instance_id": 36, "class_name": "car", "x1": 1596, "y1": 18, "x2": 1641, "y2": 45, "logit": 0.0}, "37": {"instance_id": 37, "class_name": "car", "x1": 1790, "y1": 40, "x2": 1856, "y2": 75, "logit": 0.0}, "38": {"instance_id": 38, "class_name": "car", "x1": 1710, "y1": 7, "x2": 1723, "y2": 24, "logit": 0.0}, "39": {"instance_id": 39, "class_name": "car", "x1": 1105, "y1": 0, "x2": 1135, "y2": 16, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}, "40": {"instance_id": 40, "class_name": "car", "x1": 936, "y1": 0, "x2": 955, "y2": 14, "logit": 0.0}}}