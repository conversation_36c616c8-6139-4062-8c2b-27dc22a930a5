{"mask_name": "mask_00098.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"22": {"instance_id": 22, "class_name": "car", "x1": 1080, "y1": 90, "x2": 1129, "y2": 133, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1018, "y1": 127, "x2": 1071, "y2": 182, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1384, "y1": 20, "x2": 1427, "y2": 45, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1300, "y1": 0, "x2": 1365, "y2": 11, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1267, "y1": 54, "x2": 1316, "y2": 89, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 906, "y1": 100, "x2": 954, "y2": 150, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 864, "y1": 27, "x2": 894, "y2": 51, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 920, "y1": 58, "x2": 955, "y2": 91, "logit": 0.0}, "28": {"instance_id": 28, "class_name": "car", "x1": 864, "y1": 28, "x2": 894, "y2": 50, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 963, "y1": 35, "x2": 996, "y2": 61, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1102, "y1": 0, "x2": 1131, "y2": 21, "logit": 0.0}, "33": {"instance_id": 33, "class_name": "car", "x1": 865, "y1": 28, "x2": 894, "y2": 51, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 921, "y1": 58, "x2": 956, "y2": 91, "logit": 0.0}}}