{"mask_name": "mask_00014.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "car", "x1": 1147, "y1": 29, "x2": 1168, "y2": 48, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1147, "y1": 177, "x2": 1223, "y2": 241, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1035, "y1": 185, "x2": 1102, "y2": 252, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1619, "y1": 40, "x2": 1663, "y2": 62, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 984, "y1": 51, "x2": 1013, "y2": 76, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1013, "y1": 88, "x2": 1057, "y2": 127, "logit": 0.0}, "7": {"instance_id": 7, "class_name": "car", "x1": 1228, "y1": 0, "x2": 1251, "y2": 15, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1542, "y1": 37, "x2": 1587, "y2": 62, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1310, "y1": 3, "x2": 1340, "y2": 25, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1046, "y1": 48, "x2": 1076, "y2": 72, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 924, "y1": 6, "x2": 941, "y2": 21, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1556, "y1": 34, "x2": 1592, "y2": 51, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 968, "y1": 0, "x2": 984, "y2": 17, "logit": 0.0}, "14": {"instance_id": 14, "class_name": "car", "x1": 1556, "y1": 33, "x2": 1592, "y2": 51, "logit": 0.0}, "15": {"instance_id": 15, "class_name": "car", "x1": 1169, "y1": 0, "x2": 1177, "y2": 16, "logit": 0.0}, "16": {"instance_id": 16, "class_name": "car", "x1": 1009, "y1": 0, "x2": 1024, "y2": 6, "logit": 0.0}}}