{"mask_name": "mask_00084.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"22": {"instance_id": 22, "class_name": "car", "x1": 1047, "y1": 47, "x2": 1080, "y2": 76, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 1011, "y1": 68, "x2": 1047, "y2": 104, "logit": 0.0}, "29": {"instance_id": 29, "class_name": "car", "x1": 1562, "y1": 51, "x2": 1620, "y2": 83, "logit": 0.0}, "26": {"instance_id": 26, "class_name": "car", "x1": 1423, "y1": 2, "x2": 1465, "y2": 28, "logit": 0.0}, "25": {"instance_id": 25, "class_name": "car", "x1": 1190, "y1": 27, "x2": 1227, "y2": 53, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 914, "y1": 60, "x2": 951, "y2": 97, "logit": 0.0}, "30": {"instance_id": 30, "class_name": "car", "x1": 880, "y1": 3, "x2": 903, "y2": 32, "logit": 0.0}, "31": {"instance_id": 31, "class_name": "car", "x1": 941, "y1": 23, "x2": 978, "y2": 59, "logit": 0.0}, "28": {"instance_id": 28, "class_name": "car", "x1": 879, "y1": 13, "x2": 903, "y2": 33, "logit": 0.0}, "17": {"instance_id": 17, "class_name": "car", "x1": 953, "y1": 23, "x2": 977, "y2": 41, "logit": 0.0}, "32": {"instance_id": 32, "class_name": "car", "x1": 1072, "y1": 0, "x2": 1097, "y2": 12, "logit": 0.0}, "33": {"instance_id": 33, "class_name": "car", "x1": 879, "y1": 14, "x2": 903, "y2": 32, "logit": 0.0}, "21": {"instance_id": 21, "class_name": "car", "x1": 940, "y1": 34, "x2": 968, "y2": 60, "logit": 0.0}}}