{"mask_name": "mask_00017.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "car", "x1": 1156, "y1": 31, "x2": 1179, "y2": 51, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1167, "y1": 204, "x2": 1255, "y2": 279, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1041, "y1": 214, "x2": 1116, "y2": 292, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1591, "y1": 36, "x2": 1631, "y2": 57, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 987, "y1": 54, "x2": 1019, "y2": 82, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1018, "y1": 97, "x2": 1064, "y2": 138, "logit": 0.0}, "7": {"instance_id": 7, "class_name": "car", "x1": 1217, "y1": 0, "x2": 1239, "y2": 14, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1512, "y1": 31, "x2": 1555, "y2": 55, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1295, "y1": 0, "x2": 1326, "y2": 24, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1053, "y1": 52, "x2": 1084, "y2": 78, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 926, "y1": 6, "x2": 944, "y2": 22, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1539, "y1": 26, "x2": 1566, "y2": 48, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 969, "y1": 3, "x2": 987, "y2": 18, "logit": 0.0}, "14": {"instance_id": 14, "class_name": "car", "x1": 1540, "y1": 27, "x2": 1566, "y2": 46, "logit": 0.0}, "15": {"instance_id": 15, "class_name": "car", "x1": 1170, "y1": 0, "x2": 1181, "y2": 16, "logit": 0.0}, "16": {"instance_id": 16, "class_name": "car", "x1": 0, "y1": 0, "x2": 0, "y2": 0, "logit": 0.0}}}