{"mask_name": "mask_00004.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "car", "x1": 1113, "y1": 21, "x2": 1138, "y2": 41, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1091, "y1": 118, "x2": 1149, "y2": 164, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1017, "y1": 123, "x2": 1066, "y2": 172, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1731, "y1": 60, "x2": 1785, "y2": 86, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 970, "y1": 40, "x2": 995, "y2": 63, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 996, "y1": 68, "x2": 1033, "y2": 98, "logit": 0.0}, "7": {"instance_id": 7, "class_name": "car", "x1": 1263, "y1": 10, "x2": 1287, "y2": 25, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1661, "y1": 60, "x2": 1709, "y2": 86, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1351, "y1": 15, "x2": 1385, "y2": 36, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1023, "y1": 39, "x2": 1047, "y2": 59, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 913, "y1": 9, "x2": 928, "y2": 19, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1647, "y1": 51, "x2": 1677, "y2": 72, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 951, "y1": 8, "x2": 965, "y2": 16, "logit": 0.0}, "14": {"instance_id": 14, "class_name": "car", "x1": 1647, "y1": 50, "x2": 1679, "y2": 72, "logit": 0.0}, "15": {"instance_id": 15, "class_name": "car", "x1": 1165, "y1": 0, "x2": 1176, "y2": 10, "logit": 0.0}, "16": {"instance_id": 16, "class_name": "car", "x1": 983, "y1": 0, "x2": 1002, "y2": 7, "logit": 0.0}}}