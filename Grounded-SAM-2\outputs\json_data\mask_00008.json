{"mask_name": "mask_00008.npy", "mask_height": 1080, "mask_width": 1920, "promote_type": "mask", "labels": {"1": {"instance_id": 1, "class_name": "car", "x1": 1128, "y1": 23, "x2": 1155, "y2": 43, "logit": 0.0}, "2": {"instance_id": 2, "class_name": "car", "x1": 1113, "y1": 139, "x2": 1174, "y2": 189, "logit": 0.0}, "3": {"instance_id": 3, "class_name": "car", "x1": 1024, "y1": 143, "x2": 1079, "y2": 198, "logit": 0.0}, "4": {"instance_id": 4, "class_name": "car", "x1": 1682, "y1": 52, "x2": 1731, "y2": 76, "logit": 0.0}, "5": {"instance_id": 5, "class_name": "car", "x1": 975, "y1": 45, "x2": 1002, "y2": 68, "logit": 0.0}, "6": {"instance_id": 6, "class_name": "car", "x1": 1003, "y1": 76, "x2": 1042, "y2": 108, "logit": 0.0}, "7": {"instance_id": 7, "class_name": "car", "x1": 1247, "y1": 7, "x2": 1271, "y2": 21, "logit": 0.0}, "8": {"instance_id": 8, "class_name": "car", "x1": 1608, "y1": 49, "x2": 1651, "y2": 75, "logit": 0.0}, "9": {"instance_id": 9, "class_name": "car", "x1": 1332, "y1": 11, "x2": 1366, "y2": 32, "logit": 0.0}, "10": {"instance_id": 10, "class_name": "car", "x1": 1031, "y1": 42, "x2": 1059, "y2": 64, "logit": 0.0}, "11": {"instance_id": 11, "class_name": "car", "x1": 918, "y1": 8, "x2": 933, "y2": 20, "logit": 0.0}, "12": {"instance_id": 12, "class_name": "car", "x1": 1605, "y1": 48, "x2": 1623, "y2": 63, "logit": 0.0}, "13": {"instance_id": 13, "class_name": "car", "x1": 959, "y1": 5, "x2": 973, "y2": 16, "logit": 0.0}, "14": {"instance_id": 14, "class_name": "car", "x1": 1605, "y1": 47, "x2": 1626, "y2": 66, "logit": 0.0}, "15": {"instance_id": 15, "class_name": "car", "x1": 1170, "y1": 0, "x2": 1176, "y2": 9, "logit": 0.0}, "16": {"instance_id": 16, "class_name": "car", "x1": 997, "y1": 0, "x2": 1012, "y2": 7, "logit": 0.0}}}